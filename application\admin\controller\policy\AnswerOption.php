<?php

namespace app\admin\controller\policy;

use app\common\controller\Backend;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 答案选项管理
 *
 * @icon fa fa-check-square-o
 */
class AnswerOption extends Backend
{
    /**
     * AnswerOption模型对象
     * @var \app\admin\model\policy\AnswerOption
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\policy\AnswerOption;
        $this->view->assign("statusList", $this->model->getStatusList());
        
        // 获取问题列表
        $questionList = \app\admin\model\policy\Question::with('questionnaire')
            ->where('status', 'normal')
            ->select();
        $questionOptions = [];
        foreach ($questionList as $question) {
            if ($question->questionnaire) {
                $questionOptions[$question->id] = $question->questionnaire->title . ' - ' . $question->title;
            } else {
                $questionOptions[$question->id] = '未知问卷 - ' . $question->title;
            }
        }
        $this->view->assign("questionList", $questionOptions);
        
        // 获取政策列表
        $policyList = \app\admin\model\policy\Policy::where('status', 'normal')
            ->column('title', 'id');
        $this->view->assign("policyList", $policyList);
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            // 在 buildparams 之前手动处理 filter 和 op
            $filter = $this->request->request('filter');
            $op = $this->request->request('op');

            if ($filter) {
                $filterData = json_decode($filter, true);
                if (isset($filterData['question.questionnaire.title'])) {
                    $filterData['pq.title'] = $filterData['question.questionnaire.title'];
                    unset($filterData['question.questionnaire.title']);
                    $this->request->get(['filter' => json_encode($filterData)]);
                }
                 if (isset($filterData['question.title'])) {
                    $filterData['q.title'] = $filterData['question.title'];
                    unset($filterData['question.title']);
                    $this->request->get(['filter' => json_encode($filterData)]);
                }
            }

            if ($op) {
                $opData = json_decode($op, true);
                if (isset($opData['question.questionnaire.title'])) {
                    $opData['pq.title'] = $opData['question.questionnaire.title'];
                    unset($opData['question.questionnaire.title']);
                    $this->request->get(['op' => json_encode($opData)]);
                }
                 if (isset($opData['question.title'])) {
                    $opData['q.title'] = $opData['question.title'];
                    unset($opData['question.title']);
                    $this->request->get(['op' => json_encode($opData)]);
                }
            }

            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            
            if ($sort === 'question.title') $sort = 'q.title';
            if ($sort === 'question.questionnaire.title') $sort = 'pq.title';

            // 手动构建查询以支持关联搜索，并获取所有需要的字段
            $query = $this->model
                ->alias('a')
                ->join('policy_question q', 'a.question_id = q.id', 'LEFT')
                ->join('policy_questionnaire pq', 'q.questionnaire_id = pq.id', 'LEFT')
                ->field('a.*, q.title as question_title, pq.title as questionnaire_title, q.id as question_real_id, pq.id as questionnaire_real_id');

            $list = $query
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            $items = $list->items();
            foreach ($items as &$row) {
                // 重新构建前端期望的嵌套数据结构
                $row['question'] = [
                    'id' => $row['question_real_id'],
                    'title' => $row['question_title'] ?: '未关联问题',
                    'questionnaire' => [
                        'id' => $row['questionnaire_real_id'],
                        'title' => $row['questionnaire_title'] ?: '未关联问卷',
                    ]
                ];
                
                // 统计信息
                $statistics = $this->model->getOptionStatistics($row['id']);
                $row['select_count'] = $statistics['select_count'];
                $row['policy_count'] = $statistics['policy_count'] ?? 0;
                $row['rule_count'] = $statistics['rule_count'] ?? 0;
            }
            unset($row);

            $result = array("total" => $list->total(), "rows" => $items);

            return json($result);
        }
        return $this->view->fetch();
    }


    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    // 验证选项数据
                    $validation = $this->model->validateOptionData($params);
                    if ($validation !== true) {
                        throw new ValidateException(implode(', ', $validation));
                    }

                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    // 验证选项数据
                    $validation = $this->model->validateOptionData($params);
                    if ($validation !== true) {
                        throw new ValidateException(implode(', ', $validation));
                    }

                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 管理政策关联
     */
    public function policies($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if ($this->request->isPost()) {
            $policyData = $this->request->post('policies/a');
            
            Db::startTrans();
            try {
                // 设置政策关联
                $row->setPolicies($ids, $policyData);
                
                Db::commit();
                $this->success('政策关联保存成功');
            } catch (\Exception $e) {
                Db::rollback();
                $this->error('保存失败：' . $e->getMessage());
            }
        }

        // 获取当前关联的政策
        $currentPolicyIds = $row->getPolicyIds($ids);
        $currentPolicies = [];
        if (!empty($currentPolicyIds)) {
            $policies = \app\admin\model\policy\Policy::where('id', 'in', $currentPolicyIds)->select();
            foreach ($policies as $policy) {
                $currentPolicies[$policy->id] = [
                    'policy' => $policy,
                    'weight' => $row->getWeightByPolicy($ids, $policy->id)
                ];
            }
        }

        // 获取所有政策
        $allPolicies = \app\admin\model\policy\Policy::where('status', 'normal')
            ->field('id,title,category,summary')
            ->select();

        $this->view->assign("row", $row);
        $this->view->assign("currentPolicies", $currentPolicies);
        $this->view->assign("allPolicies", $allPolicies);
        
        return $this->view->fetch();
    }

    /**
     * 选项统计
     */
    public function statistics($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 获取选项统计信息
        $statistics = $row->getOptionStatistics($ids);

        $this->view->assign("row", $row);
        $this->view->assign("statistics", $statistics);
        
        return $this->view->fetch();
    }

    /**
     * 批量设置政策关联
     */
    public function batchSetPolicies()
    {
        if ($this->request->isPost()) {
            $optionIds = $this->request->post('option_ids');
            $policyData = $this->request->post('policies/a');
            
            if (empty($optionIds)) {
                $this->error('请选择答案选项');
            }

            Db::startTrans();
            try {
                foreach ($optionIds as $optionId) {
                    $option = $this->model->get($optionId);
                    if ($option) {
                        $option->setPolicies($optionId, $policyData);
                    }
                }
                
                Db::commit();
                $this->success('批量设置成功');
            } catch (\Exception $e) {
                Db::rollback();
                $this->error('设置失败：' . $e->getMessage());
            }
        }

        // 获取所有政策
        $allPolicies = \app\admin\model\policy\Policy::where('status', 'normal')
            ->field('id,title,category,summary')
            ->select();

        $this->view->assign("allPolicies", $allPolicies);
        
        return $this->view->fetch();
    }
}
