<?php

namespace app\common\service;

use app\admin\model\policy\Questionnaire;
use app\admin\model\policy\Question;
use app\admin\model\policy\AnswerOption;
use app\admin\model\policy\Policy;
use app\admin\model\policy\UserAnswer;
use app\admin\model\policy\QuestionnaireResult;
use think\Db;

/**
 * 政策问卷服务类
 * 处理问卷答题的核心业务逻辑
 */
class PolicyQuestionnaireService
{
    /**
     * 验证问卷答案的完整性和有效性
     * 
     * @param int $userId 用户ID
     * @param int $questionnaireId 问卷ID
     * @param array $answers 答案数据
     * @return array|true 验证结果，true表示通过，数组表示错误信息
     */
    public static function validateAnswers($userId, $questionnaireId, $answers)
    {
        $errors = [];

        // 检查问卷是否存在且可用
        $questionnaire = Questionnaire::get($questionnaireId);
        if (!$questionnaire) {
            $errors[] = '问卷不存在';
            return $errors;
        }

        if (!$questionnaire->isAvailable()) {
            $errors[] = '问卷暂不可用';
            return $errors;
        }

        // 获取问卷的所有问题
        $questions = Question::where([
            'questionnaire_id' => $questionnaireId,
            'status' => 'normal'
        ])->select();

        $questionMap = [];
        $requiredQuestions = [];
        
        foreach ($questions as $question) {
            $questionMap[$question->id] = $question;
            if ($question->is_required) {
                $requiredQuestions[] = $question->id;
            }
        }

        // 检查必答题是否都已回答
        foreach ($requiredQuestions as $questionId) {
            if (!isset($answers[$questionId]) || empty($answers[$questionId])) {
                $question = $questionMap[$questionId];
                $errors[] = "问题「{$question->title}」为必答题，请回答";
            }
        }

        // 验证每个答案的有效性
        foreach ($answers as $questionId => $answerOptionIds) {
            if (!isset($questionMap[$questionId])) {
                $errors[] = "问题ID {$questionId} 不存在";
                continue;
            }

            $question = $questionMap[$questionId];
            
            // 确保答案是数组格式
            if (!is_array($answerOptionIds)) {
                $answerOptionIds = [$answerOptionIds];
            }

            // 检查单选题是否只选择了一个选项
            if ($question->type === 'single' && count($answerOptionIds) > 1) {
                $errors[] = "问题「{$question->title}」为单选题，只能选择一个选项";
                continue;
            }

            // 验证选项是否属于该问题
            $validOptionIds = AnswerOption::where([
                'question_id' => $questionId,
                'status' => 'normal'
            ])->column('id');

            foreach ($answerOptionIds as $optionId) {
                if (!in_array($optionId, $validOptionIds)) {
                    $errors[] = "选项ID {$optionId} 不属于问题「{$question->title}」";
                }
            }
        }

        return empty($errors) ? true : $errors;
    }

    /**
     * 计算政策推荐分数
     * 
     * @param array $answerOptionIds 用户选择的答案选项ID数组
     * @return array 政策分数数组，格式：[policy_id => score]
     */
    public static function calculatePolicyScores($answerOptionIds)
    {
        if (empty($answerOptionIds)) {
            return [];
        }

        // 获取答案选项对应的政策权重
        $policyWeights = Db::name('policy_answer_policy')
            ->alias('pap')
            ->join('policy p', 'pap.policy_id = p.id')
            ->where('pap.answer_option_id', 'in', $answerOptionIds)
            ->where('p.status', 'normal')
            ->field('pap.policy_id, pap.weight, pap.answer_option_id')
            ->select();

        $policyScores = [];
        $policyOptionCount = []; // 记录每个政策被多少个选项推荐

        foreach ($policyWeights as $weight) {
            $policyId = $weight['policy_id'];
            $score = $weight['weight'];

            if (!isset($policyScores[$policyId])) {
                $policyScores[$policyId] = 0;
                $policyOptionCount[$policyId] = 0;
            }

            $policyScores[$policyId] += $score;
            $policyOptionCount[$policyId]++;
        }

        // 根据推荐频次调整分数（被更多选项推荐的政策获得额外加分）
        foreach ($policyScores as $policyId => $score) {
            $optionCount = $policyOptionCount[$policyId];
            // 推荐频次加分：每多一个选项推荐，额外加5分
            $policyScores[$policyId] = $score + ($optionCount - 1) * 5;
        }

        // 按分数降序排序
        arsort($policyScores);

        return $policyScores;
    }

    /**
     * 生成智能推荐政策
     * 
     * @param array $answerOptionIds 用户选择的答案选项ID数组
     * @param int $limit 推荐数量限制
     * @return array 推荐政策列表
     */
    public static function generateRecommendations($answerOptionIds, $limit = 10)
    {
        // 计算政策分数
        $policyScores = self::calculatePolicyScores($answerOptionIds);

        if (empty($policyScores)) {
            return [];
        }

        // 获取前N个高分政策的详细信息
        $topPolicyIds = array_slice(array_keys($policyScores), 0, $limit);
        
        $policies = Policy::where('id', 'in', $topPolicyIds)
            ->where('status', 'normal')
            ->field('id,title,summary,category,cover_image,view_count,publish_date,tags')
            ->select();

        // 按分数排序并添加推荐分数
        $recommendations = [];
        foreach ($policies as $policy) {
            $policyArray = $policy->toArray();
            $policyArray['weight_score'] = $policyScores[$policy->id];
            $policyArray['recommendation_reason'] = self::generateRecommendationReason($policy->id, $answerOptionIds);
            $recommendations[] = $policyArray;
        }

        // 按权重分数排序
        usort($recommendations, function($a, $b) {
            return $b['weight_score'] - $a['weight_score'];
        });

        return $recommendations;
    }

    /**
     * 生成推荐理由
     * 
     * @param int $policyId 政策ID
     * @param array $answerOptionIds 用户选择的答案选项ID数组
     * @return string 推荐理由
     */
    public static function generateRecommendationReason($policyId, $answerOptionIds)
    {
        // 获取该政策关联的答案选项
        $relatedOptions = Db::name('policy_answer_policy')
            ->alias('pap')
            ->join('policy_answer_option ao', 'pap.answer_option_id = ao.id')
            ->join('policy_question q', 'ao.question_id = q.id')
            ->where('pap.policy_id', $policyId)
            ->where('pap.answer_option_id', 'in', $answerOptionIds)
            ->field('q.title as question_title, ao.title as option_title, pap.weight')
            ->select();

        if (empty($relatedOptions)) {
            return '根据您的答题情况推荐';
        }

        $reasons = [];
        foreach ($relatedOptions as $option) {
            $reasons[] = "您选择了「{$option['option_title']}」";
        }

        $reasonText = implode('、', array_slice($reasons, 0, 3)); // 最多显示3个理由
        if (count($reasons) > 3) {
            $reasonText .= '等';
        }

        return "基于{$reasonText}，为您推荐此政策";
    }

    /**
     * 处理问卷提交的完整流程
     * 
     * @param int $userId 用户ID
     * @param int $questionnaireId 问卷ID
     * @param array $answers 答案数据
     * @param int $startTime 开始时间戳
     * @return array 处理结果
     */
    public static function processQuestionnaireSubmission($userId, $questionnaireId, $answers, $startTime = null)
    {
        // 验证答案
        $validation = self::validateAnswers($userId, $questionnaireId, $answers);
        if ($validation !== true) {
            return [
                'success' => false,
                'message' => implode(', ', $validation)
            ];
        }

        Db::startTrans();
        try {
            // 保存答案
            $saveResult = UserAnswer::batchSaveAnswers($userId, $questionnaireId, $answers);
            if (!$saveResult) {
                throw new \Exception('答案保存失败');
            }

            // 检查是否完成问卷
            $isCompleted = UserAnswer::isQuestionnaireCompleted($userId, $questionnaireId);
            
            $result = [
                'success' => true,
                'completed' => $isCompleted,
                'result_generated' => false
            ];

            if ($isCompleted) {
                // 生成问卷结果
                $resultGenerated = QuestionnaireResult::generateResult($userId, $questionnaireId, $startTime);
                $result['result_generated'] = (bool)$resultGenerated;
            }

            Db::commit();
            return $result;

        } catch (\Exception $e) {
            Db::rollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取问卷完成统计
     * 
     * @param int $questionnaireId 问卷ID
     * @return array 统计数据
     */
    public static function getCompletionStatistics($questionnaireId)
    {
        // 获取问卷基本信息
        $questionnaire = Questionnaire::get($questionnaireId);
        if (!$questionnaire) {
            return null;
        }

        // 获取总问题数
        $totalQuestions = Question::where([
            'questionnaire_id' => $questionnaireId,
            'status' => 'normal'
        ])->count();

        // 获取必答问题数
        $requiredQuestions = Question::where([
            'questionnaire_id' => $questionnaireId,
            'status' => 'normal',
            'is_required' => 1
        ])->count();

        // 获取参与人数（至少回答过一个问题的用户）
        $participants = UserAnswer::where('questionnaire_id', $questionnaireId)
            ->group('user_id')
            ->count();

        // 获取完成人数
        $completions = QuestionnaireResult::where('questionnaire_id', $questionnaireId)->count();

        // 计算完成率
        $completionRate = $participants > 0 ? round($completions / $participants * 100, 2) : 0;

        return [
            'questionnaire_title' => $questionnaire->title,
            'total_questions' => $totalQuestions,
            'required_questions' => $requiredQuestions,
            'participants' => $participants,
            'completions' => $completions,
            'completion_rate' => $completionRate
        ];
    }
}
