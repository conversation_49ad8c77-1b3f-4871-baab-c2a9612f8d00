# 政策问卷API测试脚本

## 测试环境配置

```bash
# 基础URL
BASE_URL="http://your-domain.com"

# 测试用户Token（如果需要）
TOKEN="your-test-token"
```

## 政策管理API测试

### 1. 测试获取政策列表

```bash
# 基础测试
curl -X GET "${BASE_URL}/api/policy.policy/lists"

# 带参数测试
curl -X GET "${BASE_URL}/api/policy.policy/lists?page=1&limit=5&keyword=养老"

# 分类筛选测试
curl -X GET "${BASE_URL}/api/policy.policy/lists?category=elderly_care"
```

### 2. 测试获取政策详情

```bash
# 获取政策详情
curl -X GET "${BASE_URL}/api/policy.policy/detail?id=1"

# 错误测试 - 不存在的政策ID
curl -X GET "${BASE_URL}/api/policy.policy/detail?id=999"
```

### 3. 测试搜索政策

```bash
# 关键词搜索
curl -X GET "${BASE_URL}/api/policy.policy/search?keyword=养老&limit=10"

# 分类搜索
curl -X GET "${BASE_URL}/api/policy.policy/search?category=elderly_care"
```

### 4. 测试获取热门政策

```bash
curl -X GET "${BASE_URL}/api/policy.policy/hot?limit=5"
```

### 5. 测试获取最新政策

```bash
curl -X GET "${BASE_URL}/api/policy.policy/latest?limit=5"
```

### 6. 测试获取政策分类

```bash
curl -X GET "${BASE_URL}/api/policy.policy/categories"
```

### 7. 测试获取推荐政策

```bash
curl -X POST "${BASE_URL}/api/policy.policy/recommend" \
  -H "Content-Type: application/json" \
  -d '{
    "answer_option_ids": [1, 2, 3],
    "limit": 10
  }'
```

### 8. 测试获取相关政策

```bash
curl -X GET "${BASE_URL}/api/policy.policy/related?id=1&limit=5"
```

## 问卷管理API测试

### 1. 测试获取问卷列表

```bash
# 基础测试
curl -X GET "${BASE_URL}/api/policy.questionnaire/lists"

# 带参数测试
curl -X GET "${BASE_URL}/api/policy.questionnaire/lists?page=1&limit=5&keyword=养老"
```

### 2. 测试获取问卷详情

```bash
# 获取问卷详情
curl -X GET "${BASE_URL}/api/policy.questionnaire/detail?id=1"

# 错误测试 - 不存在的问卷ID
curl -X GET "${BASE_URL}/api/policy.questionnaire/detail?id=999"
```

### 3. 测试提交问卷答案（匿名用户）

```bash
curl -X POST "${BASE_URL}/api/policy.questionnaire/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "questionnaire_id": 1,
    "answers": [
      {
        "question_id": 1,
        "option_ids": [1]
      },
      {
        "question_id": 2,
        "option_ids": [3]
      },
      {
        "question_id": 3,
        "option_ids": [1, 2]
      }
    ],
    "start_time": 1640995200,
    "session_id": "guest_test_123"
  }'
```

### 4. 测试提交问卷答案（登录用户）

```bash
curl -X POST "${BASE_URL}/api/policy.questionnaire/submit" \
  -H "Content-Type: application/json" \
  -H "token: ${TOKEN}" \
  -d '{
    "questionnaire_id": 1,
    "answers": [
      {
        "question_id": 1,
        "option_ids": [1]
      },
      {
        "question_id": 2,
        "option_ids": [3]
      }
    ],
    "start_time": 1640995200
  }'
```

### 5. 测试获取问卷结果（匿名用户）

```bash
curl -X GET "${BASE_URL}/api/policy.questionnaire/getResult?questionnaire_id=1&session_id=guest_test_123"
```

### 6. 测试获取问卷结果（登录用户）

```bash
curl -X GET "${BASE_URL}/api/policy.questionnaire/getResult?questionnaire_id=1" \
  -H "token: ${TOKEN}"
```

### 7. 测试获取答题记录

```bash
# 匿名用户
curl -X GET "${BASE_URL}/api/policy.questionnaire/answers?questionnaire_id=1&session_id=guest_test_123"

# 登录用户
curl -X GET "${BASE_URL}/api/policy.questionnaire/answers?questionnaire_id=1" \
  -H "token: ${TOKEN}"
```

### 8. 测试重新答题

```bash
# 匿名用户
curl -X POST "${BASE_URL}/api/policy.questionnaire/reset" \
  -H "Content-Type: application/json" \
  -d '{
    "questionnaire_id": 1,
    "session_id": "guest_test_123"
  }'

# 登录用户
curl -X POST "${BASE_URL}/api/policy.questionnaire/reset" \
  -H "Content-Type: application/json" \
  -H "token: ${TOKEN}" \
  -d '{
    "questionnaire_id": 1
  }'
```

### 9. 测试获取问卷统计

```bash
curl -X GET "${BASE_URL}/api/policy.questionnaire/statistics?questionnaire_id=1"
```

## 完整测试流程

### 匿名用户完整答题流程测试

```bash
#!/bin/bash

BASE_URL="http://your-domain.com"
SESSION_ID="test_session_$(date +%s)"

echo "=== 匿名用户完整答题流程测试 ==="

# 1. 获取问卷列表
echo "1. 获取问卷列表"
curl -s -X GET "${BASE_URL}/api/policy.questionnaire/lists" | jq '.'

# 2. 获取问卷详情
echo "2. 获取问卷详情"
curl -s -X GET "${BASE_URL}/api/policy.questionnaire/detail?id=1" | jq '.'

# 3. 提交答案
echo "3. 提交答案"
SUBMIT_RESULT=$(curl -s -X POST "${BASE_URL}/api/policy.questionnaire/submit" \
  -H "Content-Type: application/json" \
  -d "{
    \"questionnaire_id\": 1,
    \"answers\": [
      {\"question_id\": 1, \"option_ids\": [1]},
      {\"question_id\": 2, \"option_ids\": [3]}
    ],
    \"start_time\": $(date +%s),
    \"session_id\": \"${SESSION_ID}\"
  }")

echo $SUBMIT_RESULT | jq '.'

# 4. 获取结果
echo "4. 获取问卷结果"
curl -s -X GET "${BASE_URL}/api/policy.questionnaire/getResult?questionnaire_id=1&session_id=${SESSION_ID}" | jq '.'

# 5. 获取答题记录
echo "5. 获取答题记录"
curl -s -X GET "${BASE_URL}/api/policy.questionnaire/answers?questionnaire_id=1&session_id=${SESSION_ID}" | jq '.'

echo "=== 测试完成 ==="
```

### 登录用户完整答题流程测试

```bash
#!/bin/bash

BASE_URL="http://your-domain.com"
TOKEN="your-test-token"

echo "=== 登录用户完整答题流程测试 ==="

# 1. 获取问卷列表
echo "1. 获取问卷列表"
curl -s -X GET "${BASE_URL}/api/policy.questionnaire/lists" \
  -H "token: ${TOKEN}" | jq '.'

# 2. 获取问卷详情
echo "2. 获取问卷详情"
curl -s -X GET "${BASE_URL}/api/policy.questionnaire/detail?id=1" \
  -H "token: ${TOKEN}" | jq '.'

# 3. 提交答案
echo "3. 提交答案"
curl -s -X POST "${BASE_URL}/api/policy.questionnaire/submit" \
  -H "Content-Type: application/json" \
  -H "token: ${TOKEN}" \
  -d "{
    \"questionnaire_id\": 1,
    \"answers\": [
      {\"question_id\": 1, \"option_ids\": [2]},
      {\"question_id\": 2, \"option_ids\": [1, 3]}
    ],
    \"start_time\": $(date +%s)
  }" | jq '.'

# 4. 获取结果
echo "4. 获取问卷结果"
curl -s -X GET "${BASE_URL}/api/policy.questionnaire/getResult?questionnaire_id=1" \
  -H "token: ${TOKEN}" | jq '.'

echo "=== 测试完成 ==="
```

## 错误测试用例

### 参数验证测试

```bash
# 缺少必填参数
curl -X GET "${BASE_URL}/api/policy.policy/detail"

# 无效的问卷ID
curl -X GET "${BASE_URL}/api/policy.questionnaire/detail?id=abc"

# 空的答案数组
curl -X POST "${BASE_URL}/api/policy.questionnaire/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "questionnaire_id": 1,
    "answers": []
  }'

# 无效的答案格式
curl -X POST "${BASE_URL}/api/policy.questionnaire/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "questionnaire_id": 1,
    "answers": "invalid"
  }'
```

## 性能测试

### 并发测试

```bash
# 使用ab工具进行并发测试
ab -n 100 -c 10 "${BASE_URL}/api/policy.policy/lists"

# 使用wrk工具进行压力测试
wrk -t12 -c400 -d30s "${BASE_URL}/api/policy.policy/lists"
```

## 测试数据准备

在运行测试之前，请确保数据库中有以下测试数据：

1. 至少1个有效的问卷记录
2. 问卷包含2-3个问题
3. 每个问题有2-4个选项
4. 至少5个政策记录
5. 配置好政策规则和条件

## 注意事项

1. 替换 `${BASE_URL}` 为实际的API地址
2. 替换 `${TOKEN}` 为有效的用户token
3. 确保测试环境数据库有足够的测试数据
4. 测试完成后清理测试数据
5. 监控服务器性能和错误日志
