# 政策规则管理操作指南

## 菜单访问路径

**后台管理 → 政策问卷 → 政策规则管理**

如果看不到菜单，请按照以下步骤操作：

### 1. 添加菜单（数据库操作）

执行以下SQL脚本之一：

```sql
-- 方法1：执行完整脚本
SOURCE sql/add_policy_rule_menu.sql;

-- 方法2：执行简化脚本  
SOURCE sql/quick_add_policy_rule_menu.sql;

-- 方法3：检查菜单状态
SOURCE sql/check_policy_rule_menu.sql;
```

### 2. 手动添加菜单（后台操作）

如果SQL脚本执行失败，可以在后台手动添加：

1. **登录后台** → **权限管理** → **菜单规则**

2. **添加主菜单**（如果不存在）：
   - 规则名称：`policy`
   - 规则标题：`政策问卷`
   - 图标：`fa fa-file-text-o`
   - 类型：菜单

3. **添加子菜单**：
   - 父级：选择"政策问卷"
   - 规则名称：`policy/policyrule`
   - 规则标题：`政策规则管理`
   - 图标：`fa fa-cogs`
   - 类型：菜单

4. **添加功能权限**（在"政策规则管理"下）：
   - `policy/policyrule/index` - 查看
   - `policy/policyrule/add` - 添加
   - `policy/policyrule/edit` - 编辑
   - `policy/policyrule/del` - 删除
   - `policy/policyrule/multi` - 批量操作
   - `policy/policyrule/getquestions` - 获取问题
   - `policy/policyrule/testmatch` - 测试匹配

5. **分配权限**：
   - **权限管理** → **管理员分组**
   - 编辑"超级管理员"组
   - 勾选所有政策规则管理相关权限

## 功能使用说明

### 1. 规则列表页面

- **查看规则**：显示所有政策规则，包括规则名称、条件组合、关联政策等
- **添加规则**：点击"添加"按钮创建新规则
- **编辑规则**：点击"编辑"按钮修改现有规则
- **删除规则**：选择规则后点击"删除"
- **测试匹配**：选择规则后点击"测试匹配"验证规则逻辑

### 2. 添加/编辑规则

#### 基本信息
- **所属问卷**：选择要配置规则的问卷
- **规则名称**：给规则起一个有意义的名称
- **规则描述**：详细描述规则的用途
- **关联政策**：选择匹配此规则时要推荐的政策（可多选）
- **权重**：规则优先级，数值越大优先级越高
- **排序**：显示顺序
- **状态**：正常/隐藏

#### 条件设置
1. **选择问卷后**，系统自动加载该问卷的所有问题
2. **点击"添加条件"**，为规则添加匹配条件
3. **设置条件**：
   - **问题**：选择要设置条件的问题
   - **选项**：选择该问题的具体选项
   - **条件类型**：
     - **必须选择**：用户必须选择这个选项，规则才能匹配
     - **可选**：用户选择任意一个可选条件即可
     - **排除**：用户选择了这个选项，规则就不匹配

#### 条件组合示例

**场景1：80岁以上且需要经济补贴**
- 条件1：年龄段 = "80岁以上" (必须选择)
- 条件2：政策需求 = "经济补贴" (必须选择)
- 推荐政策：高龄津贴政策

**场景2：需要护理或医疗保障**
- 条件1：健康状况 = "需要护理" (可选)
- 条件2：政策需求 = "医疗保障" (可选)
- 推荐政策：护理补贴、医疗保险优惠

**场景3：排除年轻用户的养老政策**
- 条件1：年龄段 = "60岁以下" (排除)
- 条件2：政策需求 = "养老服务" (必须选择)
- 推荐政策：居家养老服务

### 3. 测试匹配功能

1. **选择规则**：在规则列表中选择要测试的规则
2. **点击"测试匹配"**：打开测试界面
3. **模拟答题**：按照问卷问题选择答案
4. **查看结果**：系统显示是否匹配该规则及推荐的政策

## 业务逻辑说明

### 匹配规则

1. **必须条件**：所有标记为"必须选择"的条件都必须满足
2. **可选条件**：至少要满足一个"可选"条件（如果有的话）
3. **排除条件**：不能满足任何"排除"条件
4. **权重排序**：多个规则匹配时，按权重从高到低返回政策

### 政策推荐

1. **规则匹配**：用户答题后，系统检查所有规则
2. **政策收集**：收集所有匹配规则的关联政策
3. **去重排序**：去除重复政策，按规则权重排序
4. **返回结果**：返回最终的政策推荐列表

## 常见问题

### Q1：看不到政策规则管理菜单？
**A1：** 
1. 检查是否执行了菜单添加SQL脚本
2. 确认当前用户有相应权限
3. 清除浏览器缓存并重新登录
4. 执行 `sql/check_policy_rule_menu.sql` 检查菜单状态

### Q2：添加规则时看不到问题选项？
**A2：**
1. 确认选择的问卷中有问题
2. 确认问题的状态为"正常"
3. 确认问题下有答案选项且状态为"正常"

### Q3：规则不匹配？
**A3：**
1. 检查条件设置是否正确
2. 使用"测试匹配"功能验证
3. 确认用户答案与条件设置一致
4. 检查条件类型设置（必须/可选/排除）

### Q4：政策推荐不准确？
**A4：**
1. 检查规则权重设置
2. 确认政策关联是否正确
3. 检查是否有冲突的规则
4. 验证条件组合逻辑

## 最佳实践

### 1. 规则命名
- 使用有意义的规则名称，如"高龄津贴推荐规则"
- 在描述中详细说明适用场景

### 2. 条件设置
- 优先使用"必须选择"确保精准匹配
- 合理使用"可选"条件增加灵活性
- 谨慎使用"排除"条件避免误排

### 3. 权重管理
- 重要规则设置较高权重
- 通用规则设置较低权重
- 定期检查和调整权重

### 4. 测试验证
- 每个规则创建后都要测试
- 定期验证规则匹配效果
- 根据用户反馈调整规则

## 技术支持

如有问题，请联系开发团队或查看相关技术文档。
