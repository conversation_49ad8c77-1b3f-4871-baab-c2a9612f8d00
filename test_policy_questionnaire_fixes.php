<?php
/**
 * 政策问卷模块修复验证脚本
 * 用于测试所有修复是否生效
 */

// 模拟测试数据
$testResults = [];

// 1. 测试问卷管理字段配置
echo "=== 测试问卷管理字段配置 ===\n";
$questionnaireFields = [
    'controller_visible' => ['id','title','description','cover_image','status','sort','start_time','end_time','createtime'],
    'controller_append' => ['status_text','start_time_text','end_time_text'],
    'js_fields' => ['id','title','description','cover_image','status','question_count','sort','start_time_text','end_time_text','createtime']
];

$questionnaireCheck = array_diff($questionnaireFields['js_fields'], array_merge($questionnaireFields['controller_visible'], $questionnaireFields['controller_append'], ['question_count']));
if (empty($questionnaireCheck)) {
    echo "✅ 问卷管理字段配置一致\n";
    $testResults['questionnaire'] = 'PASS';
} else {
    echo "❌ 问卷管理字段配置不一致，缺少字段: " . implode(', ', $questionnaireCheck) . "\n";
    $testResults['questionnaire'] = 'FAIL';
}

// 2. 测试问题管理字段配置
echo "\n=== 测试问题管理字段配置 ===\n";
$questionFields = [
    'controller_visible' => ['id','questionnaire_id','title','description','type','is_required','sort','status','createtime'],
    'controller_append' => ['type_text','status_text','is_required_text'],
    'js_fields' => ['id','questionnaire.title','title','description','type','is_required','option_count','sort','status','createtime']
];

// 问题管理使用关联字段，需要特殊处理
$questionCheck = [];
foreach ($questionFields['js_fields'] as $field) {
    if ($field === 'questionnaire.title' || $field === 'option_count') {
        continue; // 这些是特殊处理的字段
    }
    if (!in_array($field, array_merge($questionFields['controller_visible'], $questionFields['controller_append']))) {
        $questionCheck[] = $field;
    }
}

if (empty($questionCheck)) {
    echo "✅ 问题管理字段配置一致\n";
    $testResults['question'] = 'PASS';
} else {
    echo "❌ 问题管理字段配置不一致，缺少字段: " . implode(', ', $questionCheck) . "\n";
    $testResults['question'] = 'FAIL';
}

// 3. 测试选项管理字段配置
echo "\n=== 测试选项管理字段配置 ===\n";
$answerOptionFields = [
    'controller_visible' => ['id','question_id','title','description','sort','status','createtime'],
    'controller_append' => ['status_text'],
    'js_fields' => ['id','question.questionnaire.title','question.title','title','description','select_count','policy_count','sort','status','createtime']
];

$answerOptionCheck = [];
foreach ($answerOptionFields['js_fields'] as $field) {
    if (strpos($field, '.') !== false || in_array($field, ['select_count', 'policy_count'])) {
        continue; // 这些是关联字段或统计字段
    }
    if (!in_array($field, array_merge($answerOptionFields['controller_visible'], $answerOptionFields['controller_append']))) {
        $answerOptionCheck[] = $field;
    }
}

if (empty($answerOptionCheck)) {
    echo "✅ 选项管理字段配置一致\n";
    $testResults['answeroption'] = 'PASS';
} else {
    echo "❌ 选项管理字段配置不一致，缺少字段: " . implode(', ', $answerOptionCheck) . "\n";
    $testResults['answeroption'] = 'FAIL';
}

// 4. 测试政策管理字段配置
echo "\n=== 测试政策管理字段配置 ===\n";
$policyFields = [
    'controller_visible' => ['id','title','summary','category','tags','cover_image','source','publish_date','effective_date','status','sort','view_count','createtime'],
    'controller_append' => ['status_text','publish_date_text','effective_date_text','tags_array'],
    'js_fields' => ['id','title','summary','category','source','cover_image','publish_date_text','effective_date_text','view_count','sort','status','createtime']
];

$policyCheck = array_diff($policyFields['js_fields'], array_merge($policyFields['controller_visible'], $policyFields['controller_append']));
if (empty($policyCheck)) {
    echo "✅ 政策管理字段配置一致\n";
    $testResults['policy'] = 'PASS';
} else {
    echo "❌ 政策管理字段配置不一致，缺少字段: " . implode(', ', $policyCheck) . "\n";
    $testResults['policy'] = 'FAIL';
}

// 5. 总结测试结果
echo "\n=== 测试结果总结 ===\n";
$passCount = 0;
$totalCount = count($testResults);

foreach ($testResults as $module => $result) {
    if ($result === 'PASS') {
        $passCount++;
        echo "✅ {$module}: 通过\n";
    } else {
        echo "❌ {$module}: 失败\n";
    }
}

echo "\n总体结果: {$passCount}/{$totalCount} 通过\n";

if ($passCount === $totalCount) {
    echo "🎉 所有模块字段配置检查通过！\n";
} else {
    echo "⚠️  存在字段配置问题，需要进一步检查\n";
}

// 6. 修复建议
echo "\n=== 修复建议 ===\n";
echo "1. 确保控制器中的visible字段包含JS文件中使用的所有字段\n";
echo "2. 正确处理关联查询的空值情况\n";
echo "3. 统一使用中文字段标题\n";
echo "4. 确保统计字段在控制器中正确计算\n";
echo "5. 检查数据库中是否有相应的数据\n";

?>
