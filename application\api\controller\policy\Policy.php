<?php

namespace app\api\controller\policy;

use app\common\controller\Api;
use app\admin\model\policy\Policy as PolicyModel;


/**
 * 政策API接口
 */
class Policy extends Api
{
    // 无需登录的接口
    protected $noNeedLogin = ['lists', 'detail', 'search', 'hot', 'latest', 'categories'];
    // 无需鉴权的接口
    protected $noNeedRight = ['*'];

    /**
     * @ApiTitle (获取政策列表)
     * @ApiSummary (获取政策列表)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.policy/lists)
     * @ApiParams (name=page, type=int, require=false, description="页码，默认1")
     * @ApiParams (name=limit, type=int, require=false, description="每页数量，默认10")
     * @ApiParams (name=category, type=string, require=false, description="政策分类")
     * @ApiParams (name=keyword, type=string, require=false, description="搜索关键词")
     */
    public function lists()
    {
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        $category = $this->request->param('category', '');
        $keyword = $this->request->param('keyword', '');

        $where = [];
        $where[] = ['status', '=', 'normal'];

        if (!empty($category)) {
            $where[] = ['category', '=', $category];
        }

        if (!empty($keyword)) {
            $where[] = ['title|summary|content', 'like', '%' . $keyword . '%'];
        }

        $list = PolicyModel::where($where)
            ->field('id,title,summary,category,cover_image,view_count,publish_date,createtime')
            ->order('sort desc, id desc')
            ->paginate($limit, false, ['page' => $page]);

        $data = [
            'list' => $list->items(),
            'total' => $list->total(),
            'page' => $page,
            'limit' => $limit
        ];

        $this->success('获取成功', $data);
    }

    /**
     * @ApiTitle (获取政策详情)
     * @ApiSummary (获取政策详细信息)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.policy/detail)
     * @ApiParams (name=id, type=int, require=true, description="政策ID")
     */
    public function detail()
    {
        $id = $this->request->param('id');
        if (empty($id)) {
            $this->error('政策ID不能为空');
        }

        $policy = PolicyModel::get($id);
        if (!$policy || $policy->status !== 'normal') {
            $this->error('政策不存在');
        }

        // 增加查看次数
        $policy->incrementViewCount($id);

        $data = [
            'id' => $policy->id,
            'title' => $policy->title,
            'content' => $policy->content,
            'summary' => $policy->summary,
            'category' => $policy->category,
            'tags' => $policy->tags_array,
            'cover_image' => $policy->cover_image,
            'attachment' => $policy->attachment,
            'source' => $policy->source,
            'publish_date' => $policy->publish_date,
            'effective_date' => $policy->effective_date,
            'view_count' => $policy->view_count + 1, // 显示增加后的数量
            'createtime' => $policy->createtime
        ];

        $this->success('获取成功', $data);
    }

    /**
     * @ApiTitle (搜索政策)
     * @ApiSummary (根据关键词和分类搜索政策)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.policy/search)
     * @ApiParams (name=keyword, type=string, require=false, description="搜索关键词")
     * @ApiParams (name=category, type=string, require=false, description="政策分类")
     * @ApiParams (name=limit, type=int, require=false, description="返回数量，默认20")
     */
    public function search()
    {
        $keyword = $this->request->param('keyword', '');
        $category = $this->request->param('category', '');
        $limit = $this->request->param('limit', 20);

        $policies = PolicyModel::searchPolicies($keyword, $category, $limit);

        $this->success('搜索成功', $policies);
    }

    /**
     * @ApiTitle (获取热门政策)
     * @ApiSummary (获取查看次数最多的政策)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.policy/hot)
     * @ApiParams (name=limit, type=int, require=false, description="返回数量，默认10")
     */
    public function hot()
    {
        $limit = $this->request->param('limit', 10);

        $policies = PolicyModel::getHotPolicies($limit);

        $this->success('获取成功', $policies);
    }

    /**
     * @ApiTitle (获取最新政策)
     * @ApiSummary (获取最新发布的政策)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.policy/latest)
     * @ApiParams (name=limit, type=int, require=false, description="返回数量，默认10")
     */
    public function latest()
    {
        $limit = $this->request->param('limit', 10);

        $policies = PolicyModel::getLatestPolicies($limit);

        $this->success('获取成功', $policies);
    }

    /**
     * @ApiTitle (获取政策分类)
     * @ApiSummary (获取所有政策分类及统计)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.policy/categories)
     */
    public function categories()
    {
        $policyModel = new PolicyModel();
        $categoryList = $policyModel->getCategoryList();
        
        // 获取每个分类的政策数量
        $categories = [];
        foreach ($categoryList as $key => $name) {
            $count = PolicyModel::where([
                'status' => 'normal',
                'category' => $key
            ])->count();
            
            $categories[] = [
                'key' => $key,
                'name' => $name,
                'count' => $count
            ];
        }

        $this->success('获取成功', $categories);
    }

    /**
     * @ApiTitle (获取推荐政策)
     * @ApiSummary (根据答案选项获取推荐政策)
     * @ApiMethod (POST)
     * @ApiRoute (/api/policy.policy/recommend)
     * @ApiParams (name=answer_option_ids, type=array, require=true, description="答案选项ID数组")
     * @ApiParams (name=limit, type=int, require=false, description="返回数量，默认10")
     */
    public function recommend()
    {
        $answerOptionIds = $this->request->param('answer_option_ids');
        $limit = $this->request->param('limit', 10);

        if (empty($answerOptionIds) || !is_array($answerOptionIds)) {
            $this->error('答案选项ID不能为空');
        }

        $policies = PolicyModel::getRecommendedPolicies($answerOptionIds, $limit);

        $this->success('获取成功', $policies);
    }

    /**
     * @ApiTitle (获取政策统计)
     * @ApiSummary (获取政策的统计信息)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.policy/statistics)
     */
    public function statistics()
    {
        $statistics = PolicyModel::getPolicyStatistics();

        $this->success('获取成功', $statistics);
    }

    /**
     * @ApiTitle (获取相关政策)
     * @ApiSummary (根据政策ID获取相关政策)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.policy/related)
     * @ApiParams (name=id, type=int, require=true, description="政策ID")
     * @ApiParams (name=limit, type=int, require=false, description="返回数量，默认5")
     */
    public function related()
    {
        $id = $this->request->param('id');
        $limit = $this->request->param('limit', 5);

        if (empty($id)) {
            $this->error('政策ID不能为空');
        }

        $policy = PolicyModel::get($id);
        if (!$policy) {
            $this->error('政策不存在');
        }

        // 根据分类和标签获取相关政策
        $where = [];
        $where[] = ['status', '=', 'normal'];
        $where[] = ['id', '<>', $id];

        // 优先匹配同分类
        if (!empty($policy->category)) {
            $where[] = ['category', '=', $policy->category];
        }

        $relatedPolicies = PolicyModel::where($where)
            ->field('id,title,summary,category,cover_image,view_count,publish_date')
            ->order('view_count desc, id desc')
            ->limit($limit)
            ->select();

        // 如果同分类的政策不够，补充其他政策
        if (count($relatedPolicies) < $limit) {
            $remainLimit = $limit - count($relatedPolicies);
            $existIds = array_column($relatedPolicies, 'id');
            $existIds[] = $id;

            $additionalPolicies = PolicyModel::where([
                ['status', '=', 'normal'],
                ['id', 'not in', $existIds]
            ])
            ->field('id,title,summary,category,cover_image,view_count,publish_date')
            ->order('view_count desc, id desc')
            ->limit($remainLimit)
            ->select();

            $relatedPolicies = array_merge($relatedPolicies, $additionalPolicies);
        }

        $this->success('获取成功', $relatedPolicies);
    }
}
