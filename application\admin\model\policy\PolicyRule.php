<?php

namespace app\admin\model\policy;

use think\Model;
use traits\model\SoftDelete;

/**
 * 政策规则模型
 */
class PolicyRule extends Model
{
    use SoftDelete;

    // 表名
    protected $name = 'policy_rule';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text',
        'policy_list',
        'condition_count'
    ];

    /**
     * 状态列表
     */
    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'hidden' => __('Hidden')];
    }

    /**
     * 状态文本获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 政策列表获取器
     */
    public function getPolicyListAttr($value, $data)
    {
        $policyIds = isset($data['policy_ids']) ? $data['policy_ids'] : '';
        if (empty($policyIds)) {
            return [];
        }
        
        $ids = json_decode($policyIds, true);
        if (!is_array($ids)) {
            return [];
        }
        
        return Policy::where('id', 'in', $ids)->select();
    }

    /**
     * 条件数量获取器
     */
    public function getConditionCountAttr($value, $data)
    {
        $id = isset($data['id']) ? $data['id'] : 0;
        if (!$id) {
            return 0;
        }
        
        return PolicyRuleCondition::where('rule_id', $id)->count();
    }

    /**
     * 关联问卷
     */
    public function questionnaire()
    {
        return $this->belongsTo('app\admin\model\policy\Questionnaire', 'questionnaire_id', 'id');
    }

    /**
     * 关联条件
     */
    public function conditions()
    {
        return $this->hasMany('PolicyRuleCondition', 'rule_id', 'id');
    }

    /**
     * 获取规则详情（包含条件）
     */
    public function getDetailWithConditions($id)
    {
        return $this->with(['questionnaire', 'conditions.question', 'conditions.answerOption'])
            ->where('id', $id)
            ->find();
    }

    /**
     * 匹配用户答案
     * @param int $questionnaireId 问卷ID
     * @param array $userAnswers 用户答案 [['question_id' => 1, 'option_ids' => [1,2]], ...]
     * @return array 匹配的规则列表
     */
    public function matchUserAnswers($questionnaireId, $userAnswers)
    {
        $rules = $this->with('conditions')
            ->where('questionnaire_id', $questionnaireId)
            ->where('status', 'normal')
            ->order('weight', 'desc')
            ->order('sort', 'asc')
            ->select();

        $matchedRules = [];
        
        foreach ($rules as $rule) {
            if ($this->isRuleMatched($rule, $userAnswers)) {
                $matchedRules[] = [
                    'rule_id' => $rule->id,
                    'rule_name' => $rule->name,
                    'weight' => $rule->weight,
                    'policy_ids' => json_decode($rule->policy_ids, true) ?: []
                ];
            }
        }
        
        return $matchedRules;
    }

    /**
     * 检查规则是否匹配
     */
    private function isRuleMatched($rule, $userAnswers)
    {
        $mustConditions = [];
        $optionalConditions = [];
        $excludeConditions = [];
        
        // 分类条件
        foreach ($rule->conditions as $condition) {
            switch ($condition->condition_type) {
                case 'must':
                    $mustConditions[] = $condition;
                    break;
                case 'optional':
                    $optionalConditions[] = $condition;
                    break;
                case 'exclude':
                    $excludeConditions[] = $condition;
                    break;
            }
        }
        
        // 检查必须条件
        foreach ($mustConditions as $condition) {
            if (!$this->isConditionMatched($condition, $userAnswers)) {
                return false;
            }
        }
        
        // 检查排除条件
        foreach ($excludeConditions as $condition) {
            if ($this->isConditionMatched($condition, $userAnswers)) {
                return false;
            }
        }
        
        // 如果有可选条件，至少要匹配一个
        if (!empty($optionalConditions)) {
            $optionalMatched = false;
            foreach ($optionalConditions as $condition) {
                if ($this->isConditionMatched($condition, $userAnswers)) {
                    $optionalMatched = true;
                    break;
                }
            }
            if (!$optionalMatched) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 检查单个条件是否匹配
     */
    private function isConditionMatched($condition, $userAnswers)
    {
        foreach ($userAnswers as $answer) {
            if ($answer['question_id'] == $condition->question_id) {
                $optionIds = is_array($answer['option_ids']) ? $answer['option_ids'] : [$answer['option_ids']];
                if (in_array($condition->answer_option_id, $optionIds)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取推荐政策
     */
    public function getRecommendedPolicies($questionnaireId, $userAnswers)
    {
        $matchedRules = $this->matchUserAnswers($questionnaireId, $userAnswers);
        
        $allPolicyIds = [];
        $policyScores = [];
        
        foreach ($matchedRules as $rule) {
            foreach ($rule['policy_ids'] as $policyId) {
                $allPolicyIds[] = $policyId;
                if (!isset($policyScores[$policyId])) {
                    $policyScores[$policyId] = 0;
                }
                $policyScores[$policyId] += $rule['weight'];
            }
        }
        
        if (empty($allPolicyIds)) {
            return [];
        }
        
        // 获取政策详情并按分数排序
        $policies = Policy::where('id', 'in', array_unique($allPolicyIds))
            ->where('status', 'normal')
            ->select();
        
        $result = [];
        foreach ($policies as $policy) {
            $result[] = [
                'policy' => $policy,
                'score' => $policyScores[$policy->id] ?? 0,
                'matched_rules' => array_filter($matchedRules, function($rule) use ($policy) {
                    return in_array($policy->id, $rule['policy_ids']);
                })
            ];
        }
        
        // 按分数排序
        usort($result, function($a, $b) {
            return $b['score'] - $a['score'];
        });
        
        return $result;
    }
}
