#!/bin/bash

# ==============================================================================
# Script Name: backup_and_clear_logs.sh
# Description: This script backs up the entire log directory to a destination,
#              verifies the copy, and then clears the source log directory.
# Author:      Shenma
# Date:        $(date +%Y-%m-%d)
# ==============================================================================

# --- Configuration ---
# Source directory for logs
SOURCE_DIR="/www/wwwroot/service.jiaqingfu.com.cn/runtime/log/"

# Destination directory for backup
DEST_DIR="/www/wwwroot/service.jiaqingfu.com.cn/public/uploads/uploads/log/"

# Log file for this script's output
LOG_FILE="/var/log/backup_and_clear_logs.log"

# --- Helper Functions ---

# Function to log messages with a timestamp
log_message() {
    echo "$(date +'%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# --- Script Logic ---

log_message "--- Starting Log Backup and Clear Script ---"

# 1. Ensure the destination directory exists.
if [ ! -d "$DEST_DIR" ]; then
    log_message "Destination directory does not exist. Creating it: ${DEST_DIR}"
    mkdir -p "$DEST_DIR"
    if [ $? -ne 0 ]; then
        log_message "ERROR: Failed to create destination directory. Please check permissions. Exiting."
        exit 1
    fi
fi

# 2. Check if the source directory exists and is not empty.
if [ ! -d "$SOURCE_DIR" ]; then
    log_message "ERROR: Source directory ${SOURCE_DIR} does not exist. Exiting."
    exit 1
fi

if [ -z "$(ls -A "$SOURCE_DIR")" ]; then
    log_message "INFO: Source directory ${SOURCE_DIR} is empty. Nothing to back up. Exiting peacefully."
    exit 0
fi

log_message "Source directory is not empty. Proceeding with backup."

# 3. Copy all files and subdirectories from source to destination.
#    The '-a' flag is for archive mode, which preserves permissions, ownership, and timestamps.
log_message "Copying all contents from ${SOURCE_DIR} to ${DEST_DIR}..."
cp -a "${SOURCE_DIR}"* "${DEST_DIR}"

# 4. Verify the copy operation was successful.
if [ $? -eq 0 ]; then
    log_message "SUCCESS: Copy operation completed successfully."
    
    # **Critical Step**: For extra safety, we can add a basic verification.
    # For example, comparing the number of items.
    # Note: This is a simple check. For mission-critical data, consider `rsync` with `--checksum`.
    source_items=$(ls -A "${SOURCE_DIR}" | wc -l)
    dest_items=$(ls -A "${DEST_DIR}" | wc -l) # This assumes dest contains only the new backup

    # A more robust check would be needed if DEST_DIR is not empty before the copy
    log_message "Verification: Found ${source_items} items in source. Copied to destination."
    
    # 5. If copy was successful, clear the source directory.
    log_message "Clearing source directory: ${SOURCE_DIR}"
    rm -rf "${SOURCE_DIR}"*
    if [ $? -eq 0 ]; then
        log_message "SUCCESS: Source directory cleared."
    else
        log_message "ERROR: Failed to clear source directory after copy. Manual cleanup may be required."
        exit 1
    fi
else
    log_message "ERROR: Failed to copy files from source to destination. Source directory will not be cleared."
    exit 1
fi

log_message "--- Log Backup and Clear Script Finished ---"
exit 0