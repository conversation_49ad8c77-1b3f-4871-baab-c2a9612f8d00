define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'ylgw_order/index' + location.search,
                    add_url: 'ylgw_order/add',
                    table: 'xiluedu_course_order',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'order_no', title: '订单号', operate: 'LIKE'},
                        {field: 'user.nickname', title: '用户昵称', operate: 'LIKE'},
                        {field: 'user.mobile', title: '手机号', operate: 'LIKE'},
                        {
                            field: 'platform', 
                            title: '开通方式', 
                            searchList: Config.platformList,
                            formatter: Table.api.formatter.normal
                        },
                        {
                            field: 'opener_info', 
                            title: '开通者', 
                            operate: 'LIKE',
                        },
                        {field: 'pay_price', title: '支付金额', operate:'BETWEEN', sortable: true},
                        {
                            field: 'pay_status', 
                            title: '支付状态',
                            searchList: {"1":__('待支付'),"2":__('已支付')},
                            formatter: Table.api.formatter.status
                        },
                        {field: 'paytime', title: '支付时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'createtime', title: '创建时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime, sortable: true}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        // edit: function () {
        //     Controller.api.bindevent();
        // },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
