<?php

return [
    [
        'name'    => 'key',
        'title'   => '应用key',
        'type'    => 'string',
        'content' => [],
        'value'   => 'your key',
        'rule'    => 'required',
        'msg'     => '',
        'tip'     => '',
        'ok'      => '',
        'extend'  => '',
    ],
    [
        'name'    => 'secret',
        'title'   => '密钥secret',
        'type'    => 'string',
        'content' => [],
        'value'   => 'your secret',
        'rule'    => 'required',
        'msg'     => '',
        'tip'     => '',
        'ok'      => '',
        'extend'  => '',
    ],
    [
        'name'    => 'sign',
        'title'   => '签名',
        'type'    => 'string',
        'content' => [],
        'value'   => 'your sign',
        'rule'    => 'required',
        'msg'     => '',
        'tip'     => '',
        'ok'      => '',
        'extend'  => '',
    ],
    [
        'name'    => 'template',
        'title'   => '短信模板',
        'type'    => 'array',
        'content' => [],
        'value'   => [
            'register'           => 'SMS_114000000',
            'resetpwd'           => 'SMS_114000000',
            'changepwd'          => 'SMS_114000000',
            'changemobile'       => 'SMS_114000000',
            'profile'            => 'SMS_114000000',
            'notice'             => 'SMS_114000000',
            'mobilelogin'        => 'SMS_114000000',
            'bind'               => 'SMS_114000000',
        ],
        'rule'    => 'required',
        'msg'     => '',
        'tip'     => '',
        'ok'      => '',
        'extend'  => '',
    ],
    [
        'name'    => '__tips__',
        'title'   => '温馨提示',
        'type'    => 'string',
        'content' => [],
        'value'   => '应用key和密钥你可以通过 https://ram.console.aliyun.com/manage/ak 获取',
        'rule'    => 'required',
        'msg'     => '',
        'tip'     => '',
        'ok'      => '',
        'extend'  => '',
    ],
];
