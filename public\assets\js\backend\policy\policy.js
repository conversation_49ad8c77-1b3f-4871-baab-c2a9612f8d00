define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'policy/policy/index' + location.search,
                    add_url: 'policy/policy/add',
                    edit_url: 'policy/policy/edit',
                    del_url: 'policy/policy/del',
                    multi_url: 'policy/policy/multi',
                    import_url: 'policy/policy/import',
                    copy_url: 'policy/policy/copy',
                    statistics_url: 'policy/policy/statistics',
                    table: 'policy',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'sort',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: 'ID', operate: false, sortable: true},
                        {field: 'title', title: '政策标题', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'summary', title: '政策摘要', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'category', title: '政策分类', operate: 'LIKE'},
                        {field: 'source', title: '政策来源', operate: 'LIKE'},
                        {field: 'cover_image', title: '封面图片', operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'publish_date_text', title: '发布日期', operate:'RANGE', addclass:'datetimerange', autocomplete:false, sortable: true},
                        {field: 'effective_date_text', title: '生效日期', operate:'RANGE', addclass:'datetimerange', autocomplete:false, sortable: true},
                        {field: 'view_count', title: '查看次数', operate: false, sortable: true},
                        {field: 'sort', title: '排序', operate: false, sortable: true},
                        {field: 'status', title: '状态', searchList: {"normal":"正常","hidden":"隐藏"}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: '创建时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime, sortable: true},
                        {field: 'operate', title: '操作', table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 复制政策
            $(document).on('click', '.btn-copy', function () {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Toastr.error('请选择至少一条记录');
                    return false;
                }
                if (ids.length > 1) {
                    Toastr.error('只能选择一个政策进行复制');
                    return false;
                }

                Layer.prompt({
                    title: '复制政策',
                    formType: 0,
                    value: '',
                    placeholder: '请输入新政策标题'
                }, function(value, index) {
                    if (!value) {
                        Toastr.error('政策标题不能为空');
                        return false;
                    }

                    Fast.api.ajax({
                        url: $.fn.bootstrapTable.defaults.extend.copy_url,
                        data: {ids: ids.join(','), title: value}
                    }, function(data, ret) {
                        Layer.close(index);
                        Toastr.success(ret.msg);
                        table.bootstrapTable('refresh');
                    });
                });
            });

            // 查看统计
            $(document).on('click', '.btn-statistics', function () {
                Fast.api.open($.fn.bootstrapTable.defaults.extend.statistics_url, '政策统计', {
                    area: ['90%', '90%']
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
