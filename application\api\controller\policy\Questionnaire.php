<?php

namespace app\api\controller\policy;

use app\common\controller\Api;
use app\admin\model\policy\Questionnaire as QuestionnaireModel;
use app\admin\model\policy\Question;
use app\admin\model\policy\AnswerOption;
use app\admin\model\policy\UserAnswer;
use app\admin\model\policy\QuestionnaireResult;

use app\admin\model\policy\Policy as PolicyModel;
use think\Db;
use think\Exception;

/**
 * 政策问卷API接口
 */
class Questionnaire extends Api
{
    // 无需登录的接口
    protected $noNeedLogin = ['lists', 'detail', 'statistics'];
    // 无需鉴权的接口
    protected $noNeedRight = ['*'];

    /**
     * @ApiTitle (获取问卷列表)
     * @ApiSummary (获取所有可用的政策问卷列表)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/lists)
     * @ApiParams (name=page, type=int, require=false, description="页码，默认1")
     * @ApiParams (name=limit, type=int, require=false, description="每页数量，默认10")
     * @ApiParams (name=keyword, type=string, require=false, description="搜索关键词")
     * @ApiParams (name=status, type=string, require=false, description="状态筛选：normal-正常，hidden-隐藏")
     */
    public function lists()
    {
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        $keyword = $this->request->param('keyword', '');
        $status = $this->request->param('status', 'normal');

        $where = [];
        $where[] = ['status', '=', $status];

        if (!empty($keyword)) {
            $where[] = ['title|description', 'like', '%' . $keyword . '%'];
        }

        // 只显示在有效期内的问卷
        $currentTime = time();
        $where[] = ['start_time', '<=', $currentTime];
        $where[] = ['end_time', '>=', $currentTime];

        $list = QuestionnaireModel::where($where)
            ->field('id,title,description,cover_image,estimated_time,total_questions,start_time,end_time,createtime')
            ->order('sort desc, id desc')
            ->paginate($limit, false, ['page' => $page]);

        $data = [
            'list' => $list->items(),
            'total' => $list->total(),
            'page' => $page,
            'limit' => $limit
        ];

        $this->success('获取成功', $data);
    }

    /**
     * @ApiTitle (获取问卷详情)
     * @ApiSummary (获取问卷详细信息，包含所有问题和选项)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/detail)
     * @ApiParams (name=id, type=int, require=true, description="问卷ID")
     */
    public function detail()
    {
        $id = $this->request->param('id');
        if (empty($id)) {
            $this->error('问卷ID不能为空');
        }

        $questionnaire = QuestionnaireModel::get($id);
        if (!$questionnaire || $questionnaire->status !== 'normal') {
            $this->error('问卷不存在或已下线');
        }

        // 检查问卷是否在有效期内
        $currentTime = time();
        if ($questionnaire->start_time > $currentTime) {
            $this->error('问卷尚未开始');
        }
        if ($questionnaire->end_time < $currentTime) {
            $this->error('问卷已结束');
        }

        // 获取问题和选项
        $questions = Question::where('questionnaire_id', $id)
            ->where('status', 'normal')
            ->order('sort', 'asc')
            ->select();

        $questionData = [];
        foreach ($questions as $question) {
            $options = AnswerOption::where('question_id', $question->id)
                ->where('status', 'normal')
                ->field('id,option_text,sort')
                ->order('sort', 'asc')
                ->select();

            $questionData[] = [
                'id' => $question->id,
                'title' => $question->title,
                'description' => $question->description,
                'type' => $question->type,
                'is_required' => $question->is_required,
                'sort' => $question->sort,
                'options' => $options
            ];
        }

        $data = [
            'id' => $questionnaire->id,
            'title' => $questionnaire->title,
            'description' => $questionnaire->description,
            'cover_image' => $questionnaire->cover_image,
            'estimated_time' => $questionnaire->estimated_time,
            'total_questions' => count($questionData),
            'start_time' => $questionnaire->start_time,
            'end_time' => $questionnaire->end_time,
            'questions' => $questionData
        ];

        $this->success('获取成功', $data);
    }

    /**
     * @ApiTitle (提交问卷答案)
     * @ApiSummary (提交用户的问卷答案并获取推荐政策)
     * @ApiMethod (POST)
     * @ApiRoute (/api/policy.questionnaire/submit)
     * @ApiParams (name=questionnaire_id, type=int, require=true, description="问卷ID")
     * @ApiParams (name=answers, type=array, require=true, description="答案数组，格式：[{question_id:1,option_ids:[1,2]},{question_id:2,option_ids:[3]}]")
     * @ApiParams (name=start_time, type=int, require=false, description="开始答题时间戳")
     * @ApiParams (name=session_id, type=string, require=false, description="会话ID，用于匿名用户")
     */
    public function submit()
    {
        $questionnaireId = $this->request->param('questionnaire_id');
        $answers = $this->request->param('answers');
        $startTime = $this->request->param('start_time', time());
        $sessionId = $this->request->param('session_id', '');

        if (empty($questionnaireId)) {
            $this->error('问卷ID不能为空');
        }

        if (empty($answers) || !is_array($answers)) {
            $this->error('答案不能为空');
        }

        // 验证问卷是否存在
        $questionnaire = QuestionnaireModel::get($questionnaireId);
        if (!$questionnaire || $questionnaire->status !== 'normal') {
            $this->error('问卷不存在或已下线');
        }

        // 获取用户ID（如果已登录）
        $userId = $this->auth->isLogin() ? $this->auth->id : 0;

        // 如果未登录且没有session_id，生成一个
        if (!$userId && empty($sessionId)) {
            $sessionId = uniqid('guest_', true);
        }

        Db::startTrans();
        try {
            // 删除之前的答题记录
            $where = ['questionnaire_id' => $questionnaireId];
            if ($userId) {
                $where['user_id'] = $userId;
            } else {
                $where['session_id'] = $sessionId;
            }
            UserAnswer::where($where)->delete();
            QuestionnaireResult::where($where)->delete();

            // 保存新的答题记录
            foreach ($answers as $answer) {
                $questionId = $answer['question_id'];
                $optionIds = $answer['option_ids'];

                foreach ($optionIds as $optionId) {
                    UserAnswer::create([
                        'questionnaire_id' => $questionnaireId,
                        'question_id' => $questionId,
                        'option_id' => $optionId,
                        'user_id' => $userId,
                        'session_id' => $sessionId,
                        'createtime' => time()
                    ]);
                }
            }

            // 计算完成时间
            $completionTime = time() - $startTime;

            // 创建问卷结果记录
            QuestionnaireResult::create([
                'questionnaire_id' => $questionnaireId,
                'user_id' => $userId,
                'session_id' => $sessionId,
                'completion_time' => $completionTime,
                'total_score' => 0, // 暂时设为0，后续可以实现评分逻辑
                'matched_rules' => '[]',
                'recommended_policy_ids' => '',
                'createtime' => time()
            ]);

            Db::commit();

            $responseData = [
                'session_id' => $sessionId,
                'questionnaire_id' => $questionnaireId,
                'completion_time' => $completionTime,
                'total_score' => 0,
                'matched_rules_count' => 0,
                'recommended_policies_count' => 0
            ];

            $this->success('提交成功', $responseData);
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }

    /**
     * @ApiTitle (获取问卷结果)
     * @ApiSummary (获取用户的问卷结果和推荐政策)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/result)
     * @ApiParams (name=questionnaire_id, type=int, require=true, description="问卷ID")
     * @ApiParams (name=session_id, type=string, require=false, description="会话ID，用于匿名用户")
     */
    public function getResult()
    {
        $questionnaireId = $this->request->param('questionnaire_id');
        $sessionId = $this->request->param('session_id', '');

        if (empty($questionnaireId)) {
            $this->error('问卷ID不能为空');
        }

        $userId = $this->auth->isLogin() ? $this->auth->id : 0;

        // 构建查询条件
        $where = ['questionnaire_id' => $questionnaireId];
        if ($userId) {
            $where['user_id'] = $userId;
        } else {
            if (empty($sessionId)) {
                $this->error('会话ID不能为空');
            }
            $where['session_id'] = $sessionId;
        }

        // 获取最新的问卷结果
        $result = QuestionnaireResult::where($where)
            ->order('id', 'desc')
            ->find();

        if (!$result) {
            $this->error('未找到问卷结果');
        }

        // 获取推荐的政策详情
        $recommendedPolicies = [];
        if (!empty($result->recommended_policy_ids)) {
            $policyIds = explode(',', $result->recommended_policy_ids);
            $recommendedPolicies = PolicyModel::where('id', 'in', $policyIds)
                ->where('status', 'normal')
                ->field('id,title,summary,category,cover_image,view_count,publish_date')
                ->select();
        }

        $data = [
            'questionnaire_id' => $result->questionnaire_id,
            'completion_time' => $result->completion_time,
            'total_score' => $result->total_score,
            'matched_rules' => $result->matched_rules ? json_decode($result->matched_rules, true) : [],
            'recommended_policies' => $recommendedPolicies,
            'submit_time' => $result->createtime
        ];

        $this->success('获取成功', $data);
    }

    /**
     * @ApiTitle (获取答题记录)
     * @ApiSummary (获取用户的答题记录详情)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/answers)
     * @ApiParams (name=questionnaire_id, type=int, require=true, description="问卷ID")
     * @ApiParams (name=session_id, type=string, require=false, description="会话ID，用于匿名用户")
     */
    public function answers()
    {
        $questionnaireId = $this->request->param('questionnaire_id');
        $sessionId = $this->request->param('session_id', '');

        if (empty($questionnaireId)) {
            $this->error('问卷ID不能为空');
        }

        $userId = $this->auth->isLogin() ? $this->auth->id : 0;

        // 构建查询条件
        $where = ['questionnaire_id' => $questionnaireId];
        if ($userId) {
            $where['user_id'] = $userId;
        } else {
            if (empty($sessionId)) {
                $this->error('会话ID不能为空');
            }
            $where['session_id'] = $sessionId;
        }

        // 获取用户答题记录
        $answers = UserAnswer::where($where)
            ->with(['question', 'answerOption'])
            ->order('question_id', 'asc')
            ->select();

        $answerData = [];
        foreach ($answers as $answer) {
            $questionId = $answer->question_id;
            if (!isset($answerData[$questionId])) {
                $answerData[$questionId] = [
                    'question_id' => $questionId,
                    'question_title' => $answer->question->title,
                    'question_type' => $answer->question->type,
                    'selected_options' => []
                ];
            }

            $answerData[$questionId]['selected_options'][] = [
                'option_id' => $answer->option_id,
                'option_text' => $answer->answerOption->option_text
            ];
        }

        $this->success('获取成功', array_values($answerData));
    }

    /**
     * @ApiTitle (重新答题)
     * @ApiSummary (清除用户的答题记录，允许重新答题)
     * @ApiMethod (POST)
     * @ApiRoute (/api/policy.questionnaire/reset)
     * @ApiParams (name=questionnaire_id, type=int, require=true, description="问卷ID")
     * @ApiParams (name=session_id, type=string, require=false, description="会话ID，用于匿名用户")
     */
    public function reset()
    {
        $questionnaireId = $this->request->param('questionnaire_id');
        $sessionId = $this->request->param('session_id', '');

        if (empty($questionnaireId)) {
            $this->error('问卷ID不能为空');
        }

        $userId = $this->auth->isLogin() ? $this->auth->id : 0;

        // 构建查询条件
        $where = ['questionnaire_id' => $questionnaireId];
        if ($userId) {
            $where['user_id'] = $userId;
        } else {
            if (empty($sessionId)) {
                $this->error('会话ID不能为空');
            }
            $where['session_id'] = $sessionId;
        }

        Db::startTrans();
        try {
            // 删除答题记录
            UserAnswer::where($where)->delete();

            // 删除问卷结果
            QuestionnaireResult::where($where)->delete();

            Db::commit();
            $this->success('重置成功');
        } catch (Exception $e) {
            Db::rollback();
            $this->error('重置失败：' . $e->getMessage());
        }
    }

    /**
     * @ApiTitle (获取问卷统计)
     * @ApiSummary (获取问卷的统计信息)
     * @ApiMethod (GET)
     * @ApiRoute (/api/policy.questionnaire/statistics)
     * @ApiParams (name=questionnaire_id, type=int, require=true, description="问卷ID")
     */
    public function statistics()
    {
        $questionnaireId = $this->request->param('questionnaire_id');

        if (empty($questionnaireId)) {
            $this->error('问卷ID不能为空');
        }

        $questionnaire = QuestionnaireModel::get($questionnaireId);
        if (!$questionnaire) {
            $this->error('问卷不存在');
        }

        $statistics = $questionnaire->getStatistics($questionnaireId);

        $this->success('获取成功', $statistics);
    }
}