<?php
/**
 * 检查政策管理数据
 */

// 模拟ThinkPHP环境
define('APP_PATH', __DIR__ . '/application/');
require_once __DIR__ . '/thinkphp/start.php';

use app\admin\model\policy\Policy;

echo "=== 检查政策管理数据 ===\n\n";

try {
    // 1. 检查政策数据
    echo "1. 检查政策数据：\n";
    $policies = Policy::select();
    echo "政策总数：" . count($policies) . "\n";
    
    if (count($policies) > 0) {
        foreach ($policies as $p) {
            echo "ID: {$p->id}, 标题: {$p->title}, 分类: {$p->category}, 状态: {$p->status}\n";
            echo "  摘要: {$p->summary}\n";
            echo "  来源: {$p->source}\n";
            echo "  查看次数: {$p->view_count}\n";
            echo "\n";
        }
    } else {
        echo "没有找到政策数据，可能需要执行演示数据SQL\n";
    }
    
    // 2. 检查数据库表结构
    echo "2. 检查数据库表结构：\n";
    $db = \think\Db::connect();
    
    // 检查policy表是否存在
    $tables = $db->query("SHOW TABLES LIKE 'fa_policy'");
    if (empty($tables)) {
        echo "fa_policy表不存在！\n";
    } else {
        echo "fa_policy表存在\n";
        
        // 检查表结构
        $fields = $db->query("DESCRIBE fa_policy");
        echo "表字段：\n";
        foreach ($fields as $field) {
            echo "  {$field['Field']} - {$field['Type']} - {$field['Null']} - {$field['Default']}\n";
        }
    }
    
    // 3. 模拟控制器查询
    echo "\n3. 模拟控制器查询：\n";
    $list = Policy::where('status', 'normal')
        ->order('sort', 'desc')
        ->paginate(10);
    
    echo "查询结果数量：" . count($list) . "\n";
    echo "总数：" . $list->total() . "\n";
    
    foreach ($list as $row) {
        echo "处理政策: {$row->title}\n";
        
        // 设置可见字段
        $row->visible(['id','title','summary','category','tags','cover_image','source','publish_date','effective_date','status','sort','view_count','createtime']);
        $row->visible(['status_text','publish_date_text','effective_date_text','tags_array']);
        
        echo "  可见字段设置完成\n";
        echo "  状态文本: {$row->status_text}\n";
        echo "  发布日期文本: {$row->publish_date_text}\n";
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}

echo "=== 检查完成 ===\n";
?>
