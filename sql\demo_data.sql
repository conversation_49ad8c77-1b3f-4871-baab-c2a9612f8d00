-- 政策问卷功能演示数据

-- 1. 插入示例政策数据
INSERT INTO `fa_policy` (`title`, `content`, `summary`, `category`, `tags`, `source`, `publish_date`, `effective_date`, `status`, `sort`, `createtime`, `updatetime`) VALUES
('老年人高龄津贴政策', '为保障高龄老年人基本生活，对80周岁以上老年人发放高龄津贴...', '80周岁以上老年人可申请高龄津贴，每月100-500元不等', 'elderly_care', '高龄津贴,养老补贴', '市民政局', UNIX_TIMESTAMP('2024-01-01'), UNIX_TIMESTAMP('2024-01-01'), 'normal', 100, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('失能老人护理补贴', '对生活不能自理的老年人提供护理补贴，减轻家庭负担...', '失能老人可申请护理补贴，每月300-800元', 'elderly_care', '护理补贴,失能老人', '市民政局', UNIX_TIMESTAMP('2024-01-01'), UNIX_TIMESTAMP('2024-01-01'), 'normal', 90, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('老年人医疗保险优惠政策', '为老年人提供医疗保险费用减免和报销比例提高...', '65周岁以上老年人医保报销比例提高10%', 'medical_care', '医疗保险,老年人优惠', '市医保局', UNIX_TIMESTAMP('2024-01-01'), UNIX_TIMESTAMP('2024-01-01'), 'normal', 80, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('老年人住房改造补贴', '对老年人家庭进行适老化改造提供资金补贴...', '老年人家庭适老化改造最高补贴5000元', 'housing', '住房改造,适老化', '市住建局', UNIX_TIMESTAMP('2024-01-01'), UNIX_TIMESTAMP('2024-01-01'), 'normal', 70, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('退休人员再就业扶持政策', '鼓励有能力的退休人员重新就业，提供相关扶持...', '退休人员再就业可享受税收优惠和培训补贴', 'employment', '再就业,退休人员', '市人社局', UNIX_TIMESTAMP('2024-01-01'), UNIX_TIMESTAMP('2024-01-01'), 'normal', 60, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 2. 插入示例问卷
INSERT INTO `fa_policy_questionnaire` (`title`, `description`, `status`, `sort`, `createtime`, `updatetime`) VALUES
('养老政策需求调研问卷', '通过此问卷了解您的基本情况和养老需求，为您推荐合适的政策', 'normal', 100, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 获取问卷ID
SET @questionnaire_id = LAST_INSERT_ID();

-- 3. 插入示例问题
INSERT INTO `fa_policy_question` (`questionnaire_id`, `title`, `description`, `type`, `is_required`, `sort`, `status`, `createtime`, `updatetime`) VALUES
(@questionnaire_id, '您的年龄段是？', '请选择您当前的年龄段', 'single', 1, 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@questionnaire_id, '您的健康状况如何？', '请选择最符合您当前健康状况的选项', 'single', 1, 2, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@questionnaire_id, '您的居住情况是？', '请选择您当前的居住情况', 'single', 1, 3, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@questionnaire_id, '您希望获得哪些方面的政策支持？', '可以选择多个选项', 'multiple', 0, 4, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 4. 插入答案选项
-- 年龄段问题的选项
INSERT INTO `fa_policy_answer_option` (`question_id`, `title`, `description`, `sort`, `status`, `createtime`, `updatetime`) VALUES
((SELECT id FROM fa_policy_question WHERE title = '您的年龄段是？'), '60-65岁', '刚退休或即将退休', 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_question WHERE title = '您的年龄段是？'), '66-75岁', '退休初期', 2, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_question WHERE title = '您的年龄段是？'), '76-85岁', '高龄老人', 3, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_question WHERE title = '您的年龄段是？'), '85岁以上', '超高龄老人', 4, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 健康状况问题的选项
INSERT INTO `fa_policy_answer_option` (`question_id`, `title`, `description`, `sort`, `status`, `createtime`, `updatetime`) VALUES
((SELECT id FROM fa_policy_question WHERE title = '您的健康状况如何？'), '身体健康', '生活完全自理', 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_question WHERE title = '您的健康状况如何？'), '轻微不适', '偶有小病，基本自理', 2, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_question WHERE title = '您的健康状况如何？'), '慢性疾病', '有慢性病，需要定期治疗', 3, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_question WHERE title = '您的健康状况如何？'), '生活不便', '生活部分不能自理', 4, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 居住情况问题的选项
INSERT INTO `fa_policy_answer_option` (`question_id`, `title`, `description`, `sort`, `status`, `createtime`, `updatetime`) VALUES
((SELECT id FROM fa_policy_question WHERE title = '您的居住情况是？'), '与子女同住', '和子女住在一起', 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_question WHERE title = '您的居住情况是？'), '老两口独居', '夫妻两人独立居住', 2, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_question WHERE title = '您的居住情况是？'), '独自居住', '一个人独立居住', 3, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_question WHERE title = '您的居住情况是？'), '养老院', '住在养老机构', 4, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 政策支持需求问题的选项
INSERT INTO `fa_policy_answer_option` (`question_id`, `title`, `description`, `sort`, `status`, `createtime`, `updatetime`) VALUES
((SELECT id FROM fa_policy_question WHERE title = '您希望获得哪些方面的政策支持？'), '经济补贴', '希望获得各类津贴补贴', 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_question WHERE title = '您希望获得哪些方面的政策支持？'), '医疗保障', '希望获得医疗方面的支持', 2, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_question WHERE title = '您希望获得哪些方面的政策支持？'), '居住环境', '希望改善居住条件', 3, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_question WHERE title = '您希望获得哪些方面的政策支持？'), '护理服务', '希望获得护理照料服务', 4, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_question WHERE title = '您希望获得哪些方面的政策支持？'), '就业机会', '希望获得再就业机会', 5, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 5. 建立答案选项与政策的关联关系
-- 高龄老人(76-85岁)和超高龄老人(85岁以上) -> 高龄津贴政策
INSERT INTO `fa_policy_answer_policy` (`answer_option_id`, `policy_id`, `weight`, `createtime`) VALUES
((SELECT id FROM fa_policy_answer_option WHERE title = '76-85岁'), (SELECT id FROM fa_policy WHERE title = '老年人高龄津贴政策'), 10, UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_answer_option WHERE title = '85岁以上'), (SELECT id FROM fa_policy WHERE title = '老年人高龄津贴政策'), 15, UNIX_TIMESTAMP());

-- 生活不便 -> 失能老人护理补贴
INSERT INTO `fa_policy_answer_policy` (`answer_option_id`, `policy_id`, `weight`, `createtime`) VALUES
((SELECT id FROM fa_policy_answer_option WHERE title = '生活不便'), (SELECT id FROM fa_policy WHERE title = '失能老人护理补贴'), 15, UNIX_TIMESTAMP());

-- 慢性疾病 -> 医疗保险优惠政策
INSERT INTO `fa_policy_answer_policy` (`answer_option_id`, `policy_id`, `weight`, `createtime`) VALUES
((SELECT id FROM fa_policy_answer_option WHERE title = '慢性疾病'), (SELECT id FROM fa_policy WHERE title = '老年人医疗保险优惠政策'), 12, UNIX_TIMESTAMP());

-- 独自居住 -> 住房改造补贴
INSERT INTO `fa_policy_answer_policy` (`answer_option_id`, `policy_id`, `weight`, `createtime`) VALUES
((SELECT id FROM fa_policy_answer_option WHERE title = '独自居住'), (SELECT id FROM fa_policy WHERE title = '老年人住房改造补贴'), 10, UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_answer_option WHERE title = '老两口独居'), (SELECT id FROM fa_policy WHERE title = '老年人住房改造补贴'), 8, UNIX_TIMESTAMP());

-- 政策需求关联
INSERT INTO `fa_policy_answer_policy` (`answer_option_id`, `policy_id`, `weight`, `createtime`) VALUES
((SELECT id FROM fa_policy_answer_option WHERE title = '经济补贴'), (SELECT id FROM fa_policy WHERE title = '老年人高龄津贴政策'), 10, UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_answer_option WHERE title = '医疗保障'), (SELECT id FROM fa_policy WHERE title = '老年人医疗保险优惠政策'), 10, UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_answer_option WHERE title = '居住环境'), (SELECT id FROM fa_policy WHERE title = '老年人住房改造补贴'), 10, UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_answer_option WHERE title = '护理服务'), (SELECT id FROM fa_policy WHERE title = '失能老人护理补贴'), 10, UNIX_TIMESTAMP()),
((SELECT id FROM fa_policy_answer_option WHERE title = '就业机会'), (SELECT id FROM fa_policy WHERE title = '退休人员再就业扶持政策'), 10, UNIX_TIMESTAMP());

-- 显示插入结果
SELECT '=== 演示数据插入完成 ===' as info;

SELECT 
    '问卷信息' as type,
    id,
    title,
    status
FROM fa_policy_questionnaire;

SELECT 
    '问题信息' as type,
    id,
    title,
    type,
    is_required
FROM fa_policy_question 
ORDER BY sort;

SELECT 
    '政策信息' as type,
    id,
    title,
    category
FROM fa_policy
ORDER BY sort DESC;

SELECT 
    '关联关系统计' as type,
    COUNT(*) as count
FROM fa_policy_answer_policy;
