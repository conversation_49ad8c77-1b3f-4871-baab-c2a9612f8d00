<?php

namespace app\admin\model\policy;

use think\Model;
use traits\model\SoftDelete;

class Questionnaire extends Model
{
    use SoftDelete;

    // 表名
    protected $name = 'policy_questionnaire';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text',
        'start_time_text',
        'end_time_text'
    ];

    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['sort' => $row[$pk]]);
        });
    }

    /**
     * 获取状态列表
     */
    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'hidden' => __('Hidden')];
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 获取开始时间文本
     */
    public function getStartTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['start_time']) ? $data['start_time'] : '');
        return is_numeric($value) && $value > 0 ? date("Y-m-d H:i:s", $value) : '';
    }

    /**
     * 获取结束时间文本
     */
    public function getEndTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['end_time']) ? $data['end_time'] : '');
        return is_numeric($value) && $value > 0 ? date("Y-m-d H:i:s", $value) : '';
    }

    /**
     * 开始时间设置器
     */
    public function setStartTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    /**
     * 结束时间设置器
     */
    public function setEndTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    /**
     * 关联问题
     */
    public function questions()
    {
        return $this->hasMany('Question', 'questionnaire_id')->order('sort', 'asc');
    }

    /**
     * 关联用户答题记录
     */
    public function userAnswers()
    {
        return $this->hasMany('UserAnswer', 'questionnaire_id');
    }

    /**
     * 关联问卷结果
     */
    public function results()
    {
        return $this->hasMany('QuestionnaireResult', 'questionnaire_id');
    }

    /**
     * 关联政策规则
     */
    public function policyRules()
    {
        return $this->hasMany('PolicyRule', 'questionnaire_id')->order('weight', 'desc')->order('sort', 'asc');
    }

    /**
     * 获取问卷统计信息
     */
    public function getStatistics($id)
    {
        $questionnaire = self::get($id);
        if (!$questionnaire) {
            return false;
        }

        $statistics = [
            'total_questions' => $questionnaire->questions()->count(),
            'total_participants' => $questionnaire->results()->group('user_id')->count(),
            'total_completions' => $questionnaire->results()->count(),
            'avg_completion_time' => $questionnaire->results()->avg('completion_time'),
        ];

        return $statistics;
    }

    /**
     * 检查问卷是否可用
     */
    public function isAvailable($id = null)
    {
        $id = $id ?: $this->id;
        $questionnaire = $id ? self::get($id) : $this;
        
        if (!$questionnaire || $questionnaire->status !== 'normal') {
            return false;
        }

        $now = time();
        
        // 检查开始时间
        if ($questionnaire->start_time && $questionnaire->start_time > $now) {
            return false;
        }
        
        // 检查结束时间
        if ($questionnaire->end_time && $questionnaire->end_time < $now) {
            return false;
        }

        return true;
    }

    /**
     * 获取问卷详情（包含问题和选项）
     */
    public function getDetailWithQuestions($id)
    {
        $questionnaire = self::get($id, ['questions.answerOptions']);
        
        if (!$questionnaire) {
            return false;
        }

        return $questionnaire;
    }
}
