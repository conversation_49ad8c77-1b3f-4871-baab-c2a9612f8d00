-- 新的政策问卷演示数据
-- 支持选项组合关联政策的逻辑

-- 1. 先执行数据表结构创建
-- SOURCE new_policy_questionnaire_structure.sql;

-- 2. 插入演示问卷
INSERT INTO `fa_policy_questionnaire` (`title`, `description`, `cover_image`, `start_time`, `end_time`, `status`, `sort`, `createtime`, `updatetime`) VALUES
('老年人政策需求评估问卷', '通过回答几个简单问题，为您推荐最适合的养老政策', '', NULL, NULL, 'normal', 100, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

SET @questionnaire_id = LAST_INSERT_ID();

-- 3. 插入演示问题
INSERT INTO `fa_policy_question` (`questionnaire_id`, `title`, `description`, `type`, `is_required`, `sort`, `status`, `createtime`, `updatetime`) VALUES
(@questionnaire_id, '您的年龄段是？', '请选择您当前的年龄段', 'single', 1, 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@questionnaire_id, '您的健康状况如何？', '请选择最符合您情况的选项', 'single', 1, 2, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@questionnaire_id, '您希望获得哪些方面的政策支持？', '可以选择多个选项', 'multiple', 1, 3, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 获取问题ID
SET @question1_id = (SELECT id FROM fa_policy_question WHERE questionnaire_id = @questionnaire_id AND sort = 1);
SET @question2_id = (SELECT id FROM fa_policy_question WHERE questionnaire_id = @questionnaire_id AND sort = 2);
SET @question3_id = (SELECT id FROM fa_policy_question WHERE questionnaire_id = @questionnaire_id AND sort = 3);

-- 4. 插入答案选项
-- 问题1的选项（年龄段）
INSERT INTO `fa_policy_answer_option` (`question_id`, `title`, `description`, `sort`, `status`, `createtime`, `updatetime`) VALUES
(@question1_id, '60-70岁', '刚退休的年龄段', 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@question1_id, '70-80岁', '中等年龄的老年人', 2, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@question1_id, '80岁以上', '高龄老年人', 3, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 问题2的选项（健康状况）
INSERT INTO `fa_policy_answer_option` (`question_id`, `title`, `description`, `sort`, `status`, `createtime`, `updatetime`) VALUES
(@question2_id, '身体健康', '能够自理日常生活', 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@question2_id, '轻度不适', '偶尔需要帮助', 2, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@question2_id, '需要护理', '日常生活需要他人协助', 3, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 问题3的选项（政策需求）
INSERT INTO `fa_policy_answer_option` (`question_id`, `title`, `description`, `sort`, `status`, `createtime`, `updatetime`) VALUES
(@question3_id, '经济补贴', '希望获得各种津贴和补贴', 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@question3_id, '医疗保障', '希望获得医疗方面的支持', 2, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@question3_id, '生活服务', '希望获得日常生活服务', 3, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@question3_id, '就业支持', '希望获得再就业的机会', 4, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 获取选项ID
SET @age_60_70 = (SELECT id FROM fa_policy_answer_option WHERE question_id = @question1_id AND sort = 1);
SET @age_70_80 = (SELECT id FROM fa_policy_answer_option WHERE question_id = @question1_id AND sort = 2);
SET @age_80_plus = (SELECT id FROM fa_policy_answer_option WHERE question_id = @question1_id AND sort = 3);

SET @health_good = (SELECT id FROM fa_policy_answer_option WHERE question_id = @question2_id AND sort = 1);
SET @health_mild = (SELECT id FROM fa_policy_answer_option WHERE question_id = @question2_id AND sort = 2);
SET @health_care = (SELECT id FROM fa_policy_answer_option WHERE question_id = @question2_id AND sort = 3);

SET @need_money = (SELECT id FROM fa_policy_answer_option WHERE question_id = @question3_id AND sort = 1);
SET @need_medical = (SELECT id FROM fa_policy_answer_option WHERE question_id = @question3_id AND sort = 2);
SET @need_service = (SELECT id FROM fa_policy_answer_option WHERE question_id = @question3_id AND sort = 3);
SET @need_job = (SELECT id FROM fa_policy_answer_option WHERE question_id = @question3_id AND sort = 4);

-- 5. 插入演示政策
INSERT INTO `fa_policy` (`title`, `content`, `summary`, `category`, `tags`, `source`, `publish_date`, `effective_date`, `status`, `sort`, `createtime`, `updatetime`) VALUES
('老年人高龄津贴政策', '为保障高龄老年人基本生活，对80周岁以上老年人发放高龄津贴...', '80周岁以上老年人可申请高龄津贴，每月100-500元不等', 'elderly_care', '高龄津贴,养老补贴', '市民政局', UNIX_TIMESTAMP('2024-01-01'), UNIX_TIMESTAMP('2024-01-01'), 'normal', 100, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('失能老人护理补贴', '对生活不能自理的老年人提供护理补贴，减轻家庭负担...', '失能老人可申请护理补贴，每月300-800元', 'elderly_care', '护理补贴,失能老人', '市民政局', UNIX_TIMESTAMP('2024-01-01'), UNIX_TIMESTAMP('2024-01-01'), 'normal', 90, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('老年人医疗保险优惠政策', '为老年人提供医疗保险费用减免和报销比例提高...', '65周岁以上老年人医保报销比例提高10%', 'medical_care', '医疗保险,老年人优惠', '市医保局', UNIX_TIMESTAMP('2024-01-01'), UNIX_TIMESTAMP('2024-01-01'), 'normal', 80, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('老年人住房改造补贴', '对老年人家庭进行适老化改造提供资金补贴...', '老年人家庭适老化改造最高补贴5000元', 'housing', '住房改造,适老化', '市住建局', UNIX_TIMESTAMP('2024-01-01'), UNIX_TIMESTAMP('2024-01-01'), 'normal', 70, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('退休人员再就业扶持政策', '鼓励有能力的退休人员重新就业，提供相关扶持...', '退休人员再就业可享受税收优惠和培训补贴', 'employment', '再就业,退休人员', '市人社局', UNIX_TIMESTAMP('2024-01-01'), UNIX_TIMESTAMP('2024-01-01'), 'normal', 60, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('老年人居家养老服务', '为居家老年人提供生活照料、医疗护理等服务...', '符合条件的老年人可享受政府购买的居家养老服务', 'elderly_care', '居家养老,生活服务', '市民政局', UNIX_TIMESTAMP('2024-01-01'), UNIX_TIMESTAMP('2024-01-01'), 'normal', 50, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 获取政策ID
SET @policy_allowance = (SELECT id FROM fa_policy WHERE title = '老年人高龄津贴政策');
SET @policy_care = (SELECT id FROM fa_policy WHERE title = '失能老人护理补贴');
SET @policy_medical = (SELECT id FROM fa_policy WHERE title = '老年人医疗保险优惠政策');
SET @policy_housing = (SELECT id FROM fa_policy WHERE title = '老年人住房改造补贴');
SET @policy_job = (SELECT id FROM fa_policy WHERE title = '退休人员再就业扶持政策');
SET @policy_service = (SELECT id FROM fa_policy WHERE title = '老年人居家养老服务');

-- 6. 创建政策规则
-- 规则1：80岁以上 + 需要经济补贴 → 高龄津贴政策
INSERT INTO `fa_policy_rule` (`questionnaire_id`, `name`, `description`, `policy_ids`, `weight`, `status`, `sort`, `createtime`, `updatetime`) VALUES
(@questionnaire_id, '高龄津贴推荐规则', '针对80岁以上且需要经济补贴的老年人', JSON_ARRAY(@policy_allowance), 100, 'normal', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

SET @rule1_id = LAST_INSERT_ID();

-- 规则1的条件
INSERT INTO `fa_policy_rule_condition` (`rule_id`, `question_id`, `answer_option_id`, `condition_type`, `createtime`) VALUES
(@rule1_id, @question1_id, @age_80_plus, 'must', UNIX_TIMESTAMP()),
(@rule1_id, @question3_id, @need_money, 'must', UNIX_TIMESTAMP());

-- 规则2：需要护理 + 需要医疗保障 → 护理补贴 + 医疗优惠
INSERT INTO `fa_policy_rule` (`questionnaire_id`, `name`, `description`, `policy_ids`, `weight`, `status`, `sort`, `createtime`, `updatetime`) VALUES
(@questionnaire_id, '护理医疗综合支持', '针对需要护理且需要医疗保障的老年人', JSON_ARRAY(@policy_care, @policy_medical), 90, 'normal', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

SET @rule2_id = LAST_INSERT_ID();

-- 规则2的条件
INSERT INTO `fa_policy_rule_condition` (`rule_id`, `question_id`, `answer_option_id`, `condition_type`, `createtime`) VALUES
(@rule2_id, @question2_id, @health_care, 'must', UNIX_TIMESTAMP()),
(@rule2_id, @question3_id, @need_medical, 'must', UNIX_TIMESTAMP());

-- 规则3：60-70岁 + 身体健康 + 需要就业支持 → 再就业政策
INSERT INTO `fa_policy_rule` (`questionnaire_id`, `name`, `description`, `policy_ids`, `weight`, `status`, `sort`, `createtime`, `updatetime`) VALUES
(@questionnaire_id, '健康老年人就业支持', '针对身体健康且希望再就业的年轻老年人', JSON_ARRAY(@policy_job), 80, 'normal', 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

SET @rule3_id = LAST_INSERT_ID();

-- 规则3的条件
INSERT INTO `fa_policy_rule_condition` (`rule_id`, `question_id`, `answer_option_id`, `condition_type`, `createtime`) VALUES
(@rule3_id, @question1_id, @age_60_70, 'must', UNIX_TIMESTAMP()),
(@rule3_id, @question2_id, @health_good, 'must', UNIX_TIMESTAMP()),
(@rule3_id, @question3_id, @need_job, 'must', UNIX_TIMESTAMP());

-- 规则4：70-80岁 + 需要生活服务 → 居家养老服务 + 住房改造
INSERT INTO `fa_policy_rule` (`questionnaire_id`, `name`, `description`, `policy_ids`, `weight`, `status`, `sort`, `createtime`, `updatetime`) VALUES
(@questionnaire_id, '中等年龄生活支持', '针对70-80岁且需要生活服务的老年人', JSON_ARRAY(@policy_service, @policy_housing), 70, 'normal', 4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

SET @rule4_id = LAST_INSERT_ID();

-- 规则4的条件
INSERT INTO `fa_policy_rule_condition` (`rule_id`, `question_id`, `answer_option_id`, `condition_type`, `createtime`) VALUES
(@rule4_id, @question1_id, @age_70_80, 'must', UNIX_TIMESTAMP()),
(@rule4_id, @question3_id, @need_service, 'must', UNIX_TIMESTAMP());

-- 规则5：通用医疗保障规则（任何年龄 + 需要医疗保障）
INSERT INTO `fa_policy_rule` (`questionnaire_id`, `name`, `description`, `policy_ids`, `weight`, `status`, `sort`, `createtime`, `updatetime`) VALUES
(@questionnaire_id, '通用医疗保障', '针对所有需要医疗保障的老年人', JSON_ARRAY(@policy_medical), 60, 'normal', 5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

SET @rule5_id = LAST_INSERT_ID();

-- 规则5的条件（任何年龄都可以，所以设为可选）
INSERT INTO `fa_policy_rule_condition` (`rule_id`, `question_id`, `answer_option_id`, `condition_type`, `createtime`) VALUES
(@rule5_id, @question1_id, @age_60_70, 'optional', UNIX_TIMESTAMP()),
(@rule5_id, @question1_id, @age_70_80, 'optional', UNIX_TIMESTAMP()),
(@rule5_id, @question1_id, @age_80_plus, 'optional', UNIX_TIMESTAMP()),
(@rule5_id, @question3_id, @need_medical, 'must', UNIX_TIMESTAMP());

-- 显示创建结果
SELECT '=== 新的政策问卷演示数据创建完成 ===' as message;

SELECT 
    '问卷信息' as type,
    id,
    title,
    status
FROM fa_policy_questionnaire 
WHERE id = @questionnaire_id;

SELECT 
    '问题信息' as type,
    id,
    title,
    type,
    is_required
FROM fa_policy_question 
WHERE questionnaire_id = @questionnaire_id
ORDER BY sort;

SELECT 
    '政策信息' as type,
    id,
    title,
    category
FROM fa_policy
ORDER BY sort DESC;

SELECT 
    '规则信息' as type,
    pr.id,
    pr.name,
    pr.weight,
    COUNT(prc.id) as condition_count
FROM fa_policy_rule pr
LEFT JOIN fa_policy_rule_condition prc ON pr.id = prc.rule_id
WHERE pr.questionnaire_id = @questionnaire_id
GROUP BY pr.id
ORDER BY pr.weight DESC;

SELECT '=== 测试场景说明 ===' as message;
SELECT '1. 80岁以上 + 需要经济补贴 → 推荐高龄津贴政策' as scenario;
SELECT '2. 需要护理 + 需要医疗保障 → 推荐护理补贴 + 医疗优惠' as scenario;
SELECT '3. 60-70岁 + 身体健康 + 需要就业支持 → 推荐再就业政策' as scenario;
SELECT '4. 70-80岁 + 需要生活服务 → 推荐居家养老服务 + 住房改造' as scenario;
SELECT '5. 任何年龄 + 需要医疗保障 → 推荐医疗保险优惠' as scenario;
