-- 添加政策规则管理菜单到政策问卷模块
-- 执行前请先确保政策问卷主菜单已存在

-- 1. 检查政策问卷主菜单是否存在
SELECT '=== 检查政策问卷主菜单 ===' as info;

SELECT 
    id, 
    pid, 
    name, 
    title, 
    icon, 
    ismenu, 
    weigh, 
    status
FROM fa_auth_rule 
WHERE name = 'policy' OR name LIKE 'policy/%'
ORDER BY pid, weigh DESC;

-- 2. 获取政策问卷主菜单ID
SET @policy_parent_id = (SELECT id FROM fa_auth_rule WHERE name = 'policy' LIMIT 1);

-- 如果主菜单不存在，先创建
INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', 0, 'policy', '政策问卷', 'fa fa-file-text-o', '', '政策问卷管理系统', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 'normal');

-- 重新获取主菜单ID
SET @policy_parent_id = (SELECT id FROM fa_auth_rule WHERE name = 'policy' LIMIT 1);

-- 显示找到的父菜单ID
SELECT CONCAT('政策问卷主菜单ID: ', IFNULL(@policy_parent_id, '未找到')) as parent_info;

-- 3. 添加政策规则管理子菜单
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', @policy_parent_id, 'policy/policyrule', '政策规则管理', 'fa fa-cogs', '', '管理政策推荐规则和条件组合', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 50, 'normal');

-- 获取政策规则管理菜单ID
SET @policyrule_menu_id = LAST_INSERT_ID();

-- 4. 添加政策规则管理功能权限
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', @policyrule_menu_id, 'policy/policyrule/index', '查看', '', '', '查看政策规则列表', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @policyrule_menu_id, 'policy/policyrule/add', '添加', '', '', '添加新的政策规则', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @policyrule_menu_id, 'policy/policyrule/edit', '编辑', '', '', '编辑政策规则', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @policyrule_menu_id, 'policy/policyrule/del', '删除', '', '', '删除政策规则', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @policyrule_menu_id, 'policy/policyrule/multi', '批量操作', '', '', '批量操作政策规则', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @policyrule_menu_id, 'policy/policyrule/getquestions', '获取问题', '', '', '根据问卷获取问题列表', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @policyrule_menu_id, 'policy/policyrule/testmatch', '测试匹配', '', '', '测试规则匹配功能', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal');

-- 5. 获取所有新添加的权限ID
SET @new_rule_ids = (
    SELECT GROUP_CONCAT(id) 
    FROM fa_auth_rule 
    WHERE name = 'policy/policyrule' 
    OR name LIKE 'policy/policyrule/%'
);

-- 6. 为超级管理员组添加权限
-- 获取当前超级管理员的权限
SET @current_rules = (SELECT rules FROM fa_auth_group WHERE id = 1);

-- 更新超级管理员组的权限（避免重复）
UPDATE fa_auth_group 
SET rules = CASE 
    WHEN rules IS NULL OR rules = '' THEN @new_rule_ids
    WHEN rules LIKE CONCAT('%', @new_rule_ids, '%') THEN rules
    ELSE CONCAT(rules, ',', @new_rule_ids)
END
WHERE id = 1;

-- 7. 清理权限字符串（去除可能的重复逗号）
UPDATE fa_auth_group 
SET rules = TRIM(BOTH ',' FROM REPLACE(REPLACE(REPLACE(rules, ',,', ','), ',,', ','), ',,', ','))
WHERE id = 1;

-- 8. 显示添加结果
SELECT '=== 政策规则管理菜单添加完成 ===' as result;

-- 显示新添加的菜单结构
SELECT 
    r.id,
    r.pid,
    r.name,
    r.title,
    r.icon,
    r.ismenu,
    r.weigh,
    r.status,
    CASE 
        WHEN r.pid = 0 THEN '主菜单'
        WHEN r.ismenu = 1 THEN '子菜单'
        ELSE '功能权限'
    END as menu_type
FROM fa_auth_rule r 
WHERE r.name = 'policy/policyrule' 
   OR r.name LIKE 'policy/policyrule/%'
ORDER BY r.pid, r.weigh DESC, r.id;

-- 9. 显示完整的政策问卷菜单结构
SELECT '=== 完整的政策问卷菜单结构 ===' as info;

SELECT 
    r.id,
    r.pid,
    r.name,
    r.title,
    r.icon,
    r.ismenu,
    r.weigh,
    r.status,
    CASE 
        WHEN r.pid = 0 THEN '主菜单'
        WHEN r.ismenu = 1 THEN '子菜单'
        ELSE '功能权限'
    END as menu_type
FROM fa_auth_rule r 
WHERE r.name = 'policy' 
   OR r.name LIKE 'policy/%'
ORDER BY r.pid, r.weigh DESC, r.id;

-- 10. 检查超级管理员权限
SELECT '=== 超级管理员权限检查 ===' as info;

SELECT 
    g.id,
    g.name,
    CASE 
        WHEN g.rules LIKE '%policy/policyrule%' THEN '已包含政策规则管理权限'
        ELSE '未包含政策规则管理权限'
    END as permission_status,
    LENGTH(g.rules) as rules_length
FROM fa_auth_group g 
WHERE g.id = 1;

-- 11. 使用说明
SELECT '=== 使用说明 ===' as info;
SELECT '1. 菜单已添加到：后台 → 政策问卷 → 政策规则管理' as instruction;
SELECT '2. 如果看不到菜单，请清除浏览器缓存并重新登录' as instruction;
SELECT '3. 确保当前用户有相应的权限访问该菜单' as instruction;
SELECT '4. 政策规则管理用于配置选项组合与政策的关联关系' as instruction;
