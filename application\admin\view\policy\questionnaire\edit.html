<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}" placeholder="请输入问卷标题">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Description')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-description" class="form-control" rows="5" name="row[description]" cols="50" placeholder="请输入问卷描述">{$row.description|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">封面图片:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-cover_image" class="form-control" size="50" name="row[cover_image]" type="text" value="{$row.cover_image|htmlentities}" placeholder="请选择封面图片">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-cover_image" class="btn btn-danger plupload" data-input-id="c-cover_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-cover_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-cover_image" class="btn btn-primary fachoose" data-input-id="c-cover_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-cover_image"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-cover_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <select  id="c-status" data-rule="required" class="form-control selectpicker" name="row[status]">
                {foreach name="statusList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.status"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">排序权重:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sort" class="form-control" name="row[sort]" type="number" value="{$row.sort}" placeholder="数值越大排序越靠前">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">开始时间:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-start_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[start_time]" type="text" value="{$row.start_time_text}" placeholder="留空表示立即开始">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">结束时间:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-end_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[end_time]" type="text" value="{$row.end_time_text}" placeholder="留空表示永不结束">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
