<?php
// 设置应用路径
define('APP_PATH', __DIR__ . '/application/');

// 加载ThinkPHP框架启动文件
require __DIR__ . '/thinkphp/start.php';

use think\Db;
use think\Config;

// 旧的OSS地址
$old_oss_url = 'https://jiaqingfu.oss-cn-beijing.aliyuncs.com';

// 从配置文件中获取新的CDN地址
// 通常在插件配置中, 这里假设是alioss插件的配置
$new_cdn_url ='https://ylzjstatics.jiaqingfu.com.cn';

if (empty($new_cdn_url) || $new_cdn_url == $old_oss_url) {
    echo "错误: 未能从配置 addons.alioss.cdnurl 中获取到新的有效CDN地址." . PHP_EOL;
    echo "请检查 /application/extra/addons.php 文件中的配置." . PHP_EOL;
    exit;
}

echo "-- [INFO] 开始生成URL替换脚本..." . PHP_EOL;
echo "-- [INFO] 旧OSS地址: " . $old_oss_url . PHP_EOL;
echo "-- [INFO] 新CDN地址: " . $new_cdn_url . PHP_EOL;
echo "--------------------------------------------------" . PHP_EOL . PHP_EOL;

// 获取数据库名和表前缀
$db_config = Config::get('database');
$db_name = $db_config['database'];
$db_prefix = $db_config['prefix'];

// 获取所有不带前缀的表名
$tables_with_prefix = Db::query("SHOW TABLES");
$tables = array_map(function($table_row) use ($db_name) {
    return $table_row['Tables_in_' . $db_name];
}, $tables_with_prefix);


$generated_sql = [];

// 遍历所有表
foreach ($tables as $table_name) {
    // 获取表的所有字段信息
    $columns = Db::query("SHOW COLUMNS FROM `{$table_name}`");

    foreach ($columns as $column) {
        $field_name = $column['Field'];
        $field_type = strtolower($column['Type']);

        // 只处理文本相关类型字段
        if (strpos($field_type, 'char') !== false || strpos($field_type, 'text') !== false) {
            // 生成UPDATE语句
            $sql = "UPDATE `{$table_name}` SET `{$field_name}` = REPLACE(`{$field_name}`, '{$old_oss_url}', '{$new_cdn_url}') WHERE `{$field_name}` LIKE '%{$old_oss_url}%';";
            $generated_sql[] = $sql;
        }
    }
}

if (empty($generated_sql)) {
    echo "-- [INFO] 未找到任何需要更新的文本字段." . PHP_EOL;
} else {
    echo "-- [SUCCESS] 已生成以下SQL语句, 请检查后手动执行:" . PHP_EOL . PHP_EOL;
    echo implode(PHP_EOL, $generated_sql);
}

echo PHP_EOL . PHP_EOL . "--------------------------------------------------" . PHP_EOL;
echo "-- [INFO] 脚本执行完毕." . PHP_EOL;

?>