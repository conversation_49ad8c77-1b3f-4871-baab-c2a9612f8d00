<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">所属问卷:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-questionnaire_id" data-rule="required" class="form-control selectpicker" name="row[questionnaire_id]">
                <option value="">请选择问卷</option>
                {volist name="questionnaires" id="questionnaire"}
                <option value="{$questionnaire.id}" {if condition="$questionnaire.id == $row.questionnaire_id"}selected{/if}>{$questionnaire.title}</option>
                {/volist}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">规则名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}" placeholder="请输入规则名称">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">规则描述:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-description" class="form-control" rows="3" name="row[description]" placeholder="请输入规则描述">{$row.description|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">关联政策:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-policy_ids" data-rule="required" class="form-control selectpicker" multiple name="row[policy_ids][]" data-live-search="true">
                {volist name="policies" id="policy"}
                <option value="{$policy.id}" {if condition="in_array($policy.id, $row.policy_ids)"}selected{/if}>{$policy.title}</option>
                {/volist}
            </select>
            <span class="help-block">可以选择多个政策</span>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">权重:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weight" class="form-control" name="row[weight]" type="number" value="{$row.weight}" placeholder="权重越高优先级越高">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">排序:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sort" class="form-control" name="row[sort]" type="number" value="{$row.sort}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">状态:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {volist name="statusList" id="vo"}
                <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {if condition="$key == $row.status"}checked{/if}> {$vo}</label>
            {/volist}
            </div>

        </div>
    </div>
    
    <!-- 条件设置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">匹配条件:</label>
        <div class="col-xs-12 col-sm-10">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        条件组合设置
                        <button type="button" class="btn btn-success btn-xs btn-add-condition pull-right">
                            <i class="fa fa-plus"></i> 添加条件
                        </button>
                    </h4>
                </div>
                <div class="panel-body">
                    <div class="alert alert-info">
                        <strong>说明：</strong>
                        <ul class="mb-0">
                            <li><strong>必须选择：</strong>用户必须选择这个选项，规则才能匹配</li>
                            <li><strong>可选：</strong>用户选择任意一个可选条件即可，至少要有一个可选条件匹配</li>
                            <li><strong>排除：</strong>用户选择了这个选项，规则就不匹配</li>
                        </ul>
                    </div>
                    <div id="condition-container">
                        <!-- 条件将通过JavaScript动态添加 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">确定</button>
            <button type="reset" class="btn btn-default btn-embossed">重置</button>
        </div>
    </div>
</form>
<!-- 直接输出由控制器生成的完整脚本块 -->
<script type="text/javascript">
{$conditions_script}
</script>
