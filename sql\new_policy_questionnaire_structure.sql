-- 新的政策问卷数据表结构
-- 支持选项组合关联政策的逻辑

-- 1. 政策规则表（新增）- 存储选项组合与政策的关联规则
DROP TABLE IF EXISTS `fa_policy_rule`;
CREATE TABLE `fa_policy_rule` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `questionnaire_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '问卷ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '规则名称',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '规则描述',
  `policy_ids` text COMMENT '关联的政策ID列表，JSON格式',
  `weight` int(10) NOT NULL DEFAULT '0' COMMENT '权重，用于排序',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `questionnaire_id` (`questionnaire_id`),
  KEY `status` (`status`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='政策规则表';

-- 2. 政策规则条件表（新增）- 存储每个规则的具体条件（选项组合）
DROP TABLE IF EXISTS `fa_policy_rule_condition`;
CREATE TABLE `fa_policy_rule_condition` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `rule_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '规则ID',
  `question_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '问题ID',
  `answer_option_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '答案选项ID',
  `condition_type` enum('must','optional','exclude') NOT NULL DEFAULT 'must' COMMENT '条件类型：must=必须选择，optional=可选，exclude=排除',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `rule_id` (`rule_id`),
  KEY `question_id` (`question_id`),
  KEY `answer_option_id` (`answer_option_id`),
  UNIQUE KEY `rule_question_option` (`rule_id`, `question_id`, `answer_option_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='政策规则条件表';

-- 3. 用户答题记录表（修改）- 存储用户的答题记录
DROP TABLE IF EXISTS `fa_policy_user_answer`;
CREATE TABLE `fa_policy_user_answer` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `questionnaire_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '问卷ID',
  `question_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '问题ID',
  `answer_option_ids` text COMMENT '选择的答案选项ID列表，JSON格式（支持多选）',
  `answer_text` text COMMENT '文本答案（如果是文本题）',
  `session_id` varchar(50) NOT NULL DEFAULT '' COMMENT '答题会话ID',
  `ip` varchar(50) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `questionnaire_id` (`questionnaire_id`),
  KEY `question_id` (`question_id`),
  KEY `session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户答题记录表';

-- 4. 问卷结果表（修改）- 存储问卷完成后的结果和推荐政策
DROP TABLE IF EXISTS `fa_policy_questionnaire_result`;
CREATE TABLE `fa_policy_questionnaire_result` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `questionnaire_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '问卷ID',
  `session_id` varchar(50) NOT NULL DEFAULT '' COMMENT '答题会话ID',
  `answers` longtext COMMENT '完整答题记录，JSON格式',
  `matched_rules` text COMMENT '匹配的规则ID列表，JSON格式',
  `recommended_policies` longtext COMMENT '推荐的政策列表，JSON格式',
  `completion_time` int(10) NOT NULL DEFAULT '0' COMMENT '答题用时（秒）',
  `ip` varchar(50) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `questionnaire_id` (`questionnaire_id`),
  KEY `session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问卷结果表';

-- 5. 删除原有的关联表（不再需要）
DROP TABLE IF EXISTS `fa_policy_answer_policy`;

-- 6. 为现有表添加索引优化
ALTER TABLE `fa_policy_questionnaire` ADD INDEX `status_sort` (`status`, `sort`);
ALTER TABLE `fa_policy_question` ADD INDEX `questionnaire_status_sort` (`questionnaire_id`, `status`, `sort`);
ALTER TABLE `fa_policy_answer_option` ADD INDEX `question_status_sort` (`question_id`, `status`, `sort`);
ALTER TABLE `fa_policy` ADD INDEX `status_sort` (`status`, `sort`);

-- 7. 创建视图用于统计分析
CREATE OR REPLACE VIEW `v_policy_rule_statistics` AS
SELECT 
    pr.id as rule_id,
    pr.name as rule_name,
    pr.questionnaire_id,
    pq.title as questionnaire_title,
    COUNT(DISTINCT pqr.id) as match_count,
    COUNT(DISTINCT pqr.user_id) as user_count,
    pr.weight,
    pr.status
FROM fa_policy_rule pr
LEFT JOIN fa_policy_questionnaire pq ON pr.questionnaire_id = pq.id
LEFT JOIN fa_policy_questionnaire_result pqr ON FIND_IN_SET(pr.id, REPLACE(REPLACE(pqr.matched_rules, '[', ''), ']', ''))
WHERE pr.status = 'normal'
GROUP BY pr.id;

-- 8. 创建存储过程用于匹配政策规则
DELIMITER $$
CREATE PROCEDURE `sp_match_policy_rules`(
    IN p_questionnaire_id INT,
    IN p_user_answers JSON
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_rule_id INT;
    DECLARE v_match_count INT;
    DECLARE v_condition_count INT;
    DECLARE cur CURSOR FOR 
        SELECT id FROM fa_policy_rule 
        WHERE questionnaire_id = p_questionnaire_id AND status = 'normal'
        ORDER BY weight DESC, sort ASC;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 创建临时表存储匹配结果
    DROP TEMPORARY TABLE IF EXISTS temp_matched_rules;
    CREATE TEMPORARY TABLE temp_matched_rules (
        rule_id INT,
        match_score INT,
        policy_ids TEXT
    );
    
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO v_rule_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 检查规则条件是否匹配
        SELECT COUNT(*) INTO v_condition_count
        FROM fa_policy_rule_condition 
        WHERE rule_id = v_rule_id AND condition_type = 'must';
        
        SELECT COUNT(*) INTO v_match_count
        FROM fa_policy_rule_condition prc
        WHERE prc.rule_id = v_rule_id 
        AND prc.condition_type = 'must'
        AND JSON_CONTAINS(p_user_answers, JSON_OBJECT('question_id', prc.question_id, 'option_id', prc.answer_option_id));
        
        -- 如果所有必须条件都匹配，则添加到结果中
        IF v_match_count = v_condition_count THEN
            INSERT INTO temp_matched_rules (rule_id, match_score, policy_ids)
            SELECT v_rule_id, v_match_count, policy_ids 
            FROM fa_policy_rule 
            WHERE id = v_rule_id;
        END IF;
        
    END LOOP;
    CLOSE cur;
    
    -- 返回匹配结果
    SELECT * FROM temp_matched_rules ORDER BY match_score DESC;
    
END$$
DELIMITER ;

-- 显示创建结果
SELECT '=== 新的政策问卷数据表结构创建完成 ===' as message;
SELECT 'fa_policy_rule - 政策规则表' as table_info;
SELECT 'fa_policy_rule_condition - 政策规则条件表' as table_info;
SELECT 'fa_policy_user_answer - 用户答题记录表（已修改）' as table_info;
SELECT 'fa_policy_questionnaire_result - 问卷结果表（已修改）' as table_info;
