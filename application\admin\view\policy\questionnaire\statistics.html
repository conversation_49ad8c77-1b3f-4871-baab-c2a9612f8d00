<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#one" data-toggle="tab">问卷统计</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="row">
                    <div class="col-xs-12">
                        <!-- 问卷基本信息 -->
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h3 class="panel-title">{$row.title} - 统计报告</h3>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-sm-3">
                                        <div class="info-box bg-aqua">
                                            <span class="info-box-icon"><i class="fa fa-users"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">总参与人数</span>
                                                <span class="info-box-number">{$statistics.total_participants|default=0}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="info-box bg-green">
                                            <span class="info-box-icon"><i class="fa fa-check"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">完成人数</span>
                                                <span class="info-box-number">{$statistics.completed_participants|default=0}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="info-box bg-yellow">
                                            <span class="info-box-icon"><i class="fa fa-percent"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">完成率</span>
                                                <span class="info-box-number">{$statistics.completion_rate|default=0}%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="info-box bg-red">
                                            <span class="info-box-icon"><i class="fa fa-calendar"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">平均用时</span>
                                                <span class="info-box-number">{$statistics.avg_completion_time|default='--'}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 问题统计 -->
                        {if condition="isset($statistics.question_stats) && $statistics.question_stats"}
                        {volist name="statistics.question_stats" id="questionStat"}
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    问题 {$i}：{$questionStat.question.title}
                                    <span class="badge pull-right">回答人数：{$questionStat.total_answers}</span>
                                </h4>
                            </div>
                            <div class="panel-body">
                                {if condition="$questionStat.question.description"}
                                <p class="text-muted">{$questionStat.question.description}</p>
                                {/if}
                                
                                <!-- 选项统计 -->
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>选项</th>
                                                <th>选择人数</th>
                                                <th>占比</th>
                                                <th>进度条</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {volist name="questionStat.option_stats" id="optionStat"}
                                            <tr>
                                                <td>{$optionStat.option.title}</td>
                                                <td>{$optionStat.select_count}</td>
                                                <td>
                                                    {if condition="$questionStat.total_answers > 0"}
                                                    {php}echo round(($optionStat['select_count'] / $questionStat['total_answers']) * 100, 1);{/php}%
                                                    {else}
                                                    0%
                                                    {/if}
                                                </td>
                                                <td>
                                                    <div class="progress" style="margin-bottom: 0;">
                                                        <div class="progress-bar progress-bar-info" style="width: {if condition='$questionStat.total_answers > 0'}{php}echo round(($optionStat['select_count'] / $questionStat['total_answers']) * 100, 1);{/php}{else}0{/if}%"></div>
                                                    </div>
                                                </td>
                                            </tr>
                                            {/volist}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        {/volist}
                        {/if}

                        <!-- 热门政策推荐统计 -->
                        {if condition="isset($popularPolicies) && $popularPolicies"}
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <h4 class="panel-title">热门政策推荐统计</h4>
                            </div>
                            <div class="panel-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>政策名称</th>
                                                <th>推荐次数</th>
                                                <th>推荐率</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {volist name="popularPolicies" id="policy"}
                                            <tr>
                                                <td>{$policy.title}</td>
                                                <td>{$policy.recommend_count}</td>
                                                <td>{$policy.recommend_rate}%</td>
                                            </tr>
                                            {/volist}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        {/if}

                        <!-- 操作按钮 -->
                        <div class="form-group text-center">
                            <button type="button" class="btn btn-primary" onclick="window.close();">关闭统计</button>
                            <button type="button" class="btn btn-success" onclick="window.print();">打印报告</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
