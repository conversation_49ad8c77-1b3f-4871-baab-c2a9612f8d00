<?php

namespace app\admin\model\policy;

use think\Model;
use traits\model\SoftDelete;

class Policy extends Model
{
    use SoftDelete;

    // 表名
    protected $name = 'policy';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text',
        'publish_date_text',
        'effective_date_text',
        'tags_array'
    ];

    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['sort' => $row[$pk]]);
        });
    }

    /**
     * 获取状态列表
     */
    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'hidden' => __('Hidden')];
    }

    /**
     * 获取政策分类列表
     */
    public function getCategoryList()
    {
        return [
            'elderly_care' => '养老服务',
            'medical_care' => '医疗保障',
            'housing' => '住房保障',
            'employment' => '就业创业',
            'education' => '教育培训',
            'social_security' => '社会保障',
            'tax_policy' => '税收政策',
            'subsidy' => '补贴政策',
            'other' => '其他政策'
        ];
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 获取发布日期文本
     */
    public function getPublishDateTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['publish_date']) ? $data['publish_date'] : '');
        return is_numeric($value) && $value > 0 ? date("Y-m-d", $value) : '';
    }

    /**
     * 获取生效日期文本
     */
    public function getEffectiveDateTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['effective_date']) ? $data['effective_date'] : '');
        return is_numeric($value) && $value > 0 ? date("Y-m-d", $value) : '';
    }

    /**
     * 获取标签数组
     */
    public function getTagsArrayAttr($value, $data)
    {
        $tags = $value ? $value : (isset($data['tags']) ? $data['tags'] : '');
        return $tags ? explode(',', $tags) : [];
    }

    /**
     * 发布日期设置器
     */
    public function setPublishDateAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    /**
     * 生效日期设置器
     */
    public function setEffectiveDateAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    /**
     * 标签设置器
     */
    public function setTagsAttr($value)
    {
        return is_array($value) ? implode(',', $value) : $value;
    }

    /**
     * 关联答案选项（多对多）
     */
    public function answerOptions()
    {
        return $this->belongsToMany('AnswerOption', 'policy_answer_policy', 'answer_option_id', 'policy_id')
            ->withField('weight,createtime');
    }

    /**
     * 增加查看次数
     */
    public function incrementViewCount($id = null)
    {
        $id = $id ?: $this->id;
        return self::where('id', $id)->setInc('view_count');
    }

    /**
     * 根据答案选项获取推荐政策
     */
    public static function getRecommendedPolicies($answerOptionIds, $limit = 10)
    {
        if (empty($answerOptionIds)) {
            return [];
        }

        // 确保是数组格式
        if (!is_array($answerOptionIds)) {
            $answerOptionIds = explode(',', $answerOptionIds);
        }

        // 获取政策权重分数
        $policyScores = \think\Db::name('policy_answer_policy')
            ->alias('pap')
            ->join('policy p', 'pap.policy_id = p.id')
            ->where('pap.answer_option_id', 'in', $answerOptionIds)
            ->where('p.status', 'normal')
            ->field('pap.policy_id, SUM(pap.weight) as total_weight')
            ->group('pap.policy_id')
            ->order('total_weight', 'desc')
            ->limit($limit)
            ->select();

        if (empty($policyScores)) {
            return [];
        }

        // 获取政策详情
        $policyIds = array_column($policyScores, 'policy_id');
        $policies = self::where('id', 'in', $policyIds)
            ->where('status', 'normal')
            ->field('id,title,summary,category,cover_image,view_count,publish_date')
            ->select();

        // 按权重排序
        $policyMap = [];
        foreach ($policies as $policy) {
            $policyMap[$policy['id']] = $policy;
        }

        $result = [];
        foreach ($policyScores as $score) {
            if (isset($policyMap[$score['policy_id']])) {
                $policy = $policyMap[$score['policy_id']];
                $policy['weight_score'] = $score['total_weight'];
                $result[] = $policy;
            }
        }

        return $result;
    }

    /**
     * 搜索政策
     */
    public static function searchPolicies($keyword, $category = '', $limit = 20)
    {
        $where = [];
        $where[] = ['status', '=', 'normal'];

        if (!empty($keyword)) {
            $where[] = ['title|summary|content', 'like', '%' . $keyword . '%'];
        }

        if (!empty($category)) {
            $where[] = ['category', '=', $category];
        }

        return self::where($where)
            ->field('id,title,summary,category,cover_image,view_count,publish_date')
            ->order('sort desc, id desc')
            ->limit($limit)
            ->select();
    }

    /**
     * 获取热门政策
     */
    public static function getHotPolicies($limit = 10)
    {
        return self::where('status', 'normal')
            ->field('id,title,summary,category,cover_image,view_count,publish_date')
            ->order('view_count desc, id desc')
            ->limit($limit)
            ->select();
    }

    /**
     * 获取最新政策
     */
    public static function getLatestPolicies($limit = 10)
    {
        return self::where('status', 'normal')
            ->field('id,title,summary,category,cover_image,view_count,publish_date')
            ->order('publish_date desc, id desc')
            ->limit($limit)
            ->select();
    }

    /**
     * 获取政策统计信息
     */
    public static function getPolicyStatistics()
    {
        $total = self::where('status', 'normal')->count();
        $categories = self::where('status', 'normal')
            ->group('category')
            ->field('category, count(*) as count')
            ->select();

        return [
            'total' => $total,
            'categories' => $categories
        ];
    }
}
