-- 快速添加政策问卷管理菜单

-- 删除已存在的菜单（如果有）
DELETE FROM `fa_auth_rule` WHERE `name` LIKE 'policy%' OR `name` = 'policy';

-- 添加主菜单：政策问卷
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `ismenu`, `weigh`, `status`, `createtime`, `updatetime`) VALUES
('menu', 0, 'policy', '政策问卷', 'fa fa-file-text-o', 1, 100, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 获取主菜单ID
SET @policy_id = LAST_INSERT_ID();

-- 添加子菜单
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `ismenu`, `weigh`, `status`, `createtime`, `updatetime`) VALUES
('menu', @policy_id, 'policy/questionnaire', '问卷管理', 'fa fa-list-alt', 1, 90, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('menu', @policy_id, 'policy/question', '问题管理', 'fa fa-question-circle', 1, 80, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('menu', @policy_id, 'policy/answeroption', '选项管理', 'fa fa-check-square-o', 1, 70, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('menu', @policy_id, 'policy/policy', '政策管理', 'fa fa-file-text-o', 1, 60, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 获取子菜单ID
SET @questionnaire_id = (SELECT id FROM fa_auth_rule WHERE name = 'policy/questionnaire');
SET @question_id = (SELECT id FROM fa_auth_rule WHERE name = 'policy/question');
SET @answeroption_id = (SELECT id FROM fa_auth_rule WHERE name = 'policy/answeroption');
SET @policy_policy_id = (SELECT id FROM fa_auth_rule WHERE name = 'policy/policy');

-- 添加功能权限（问卷管理）
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `ismenu`, `weigh`, `status`, `createtime`, `updatetime`) VALUES
('file', @questionnaire_id, 'policy/questionnaire/index', '查看', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @questionnaire_id, 'policy/questionnaire/add', '添加', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @questionnaire_id, 'policy/questionnaire/edit', '编辑', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @questionnaire_id, 'policy/questionnaire/del', '删除', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @questionnaire_id, 'policy/questionnaire/multi', '批量操作', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @questionnaire_id, 'policy/questionnaire/copy', '复制', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @questionnaire_id, 'policy/questionnaire/statistics', '统计', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @questionnaire_id, 'policy/questionnaire/preview', '预览', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 添加功能权限（问题管理）
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `ismenu`, `weigh`, `status`, `createtime`, `updatetime`) VALUES
('file', @question_id, 'policy/question/index', '查看', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @question_id, 'policy/question/add', '添加', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @question_id, 'policy/question/edit', '编辑', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @question_id, 'policy/question/del', '删除', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @question_id, 'policy/question/multi', '批量操作', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @question_id, 'policy/question/options', '管理选项', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @question_id, 'policy/question/statistics', '统计', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @question_id, 'policy/question/copy', '复制', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 添加功能权限（选项管理）
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `ismenu`, `weigh`, `status`, `createtime`, `updatetime`) VALUES
('file', @answeroption_id, 'policy/answeroption/index', '查看', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @answeroption_id, 'policy/answeroption/add', '添加', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @answeroption_id, 'policy/answeroption/edit', '编辑', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @answeroption_id, 'policy/answeroption/del', '删除', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @answeroption_id, 'policy/answeroption/multi', '批量操作', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @answeroption_id, 'policy/answeroption/policies', '管理政策', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @answeroption_id, 'policy/answeroption/statistics', '统计', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 添加功能权限（政策管理）
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `ismenu`, `weigh`, `status`, `createtime`, `updatetime`) VALUES
('file', @policy_policy_id, 'policy/policy/index', '查看', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @policy_policy_id, 'policy/policy/add', '添加', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @policy_policy_id, 'policy/policy/edit', '编辑', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @policy_policy_id, 'policy/policy/del', '删除', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @policy_policy_id, 'policy/policy/multi', '批量操作', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @policy_policy_id, 'policy/policy/detail', '详情', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @policy_policy_id, 'policy/policy/statistics', '统计', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @policy_policy_id, 'policy/policy/search', '搜索', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('file', @policy_policy_id, 'policy/policy/copy', '复制', 0, 0, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 显示结果
SELECT '政策问卷管理菜单添加完成！' as result;

SELECT
    name,
    title,
    CASE
        WHEN pid = 0 THEN '主菜单'
        WHEN ismenu = 1 THEN '子菜单'
        ELSE '功能权限'
    END as type,
    weigh,
    status
FROM fa_auth_rule
WHERE name LIKE 'policy%' OR name = 'policy'
ORDER BY pid, weigh DESC;
