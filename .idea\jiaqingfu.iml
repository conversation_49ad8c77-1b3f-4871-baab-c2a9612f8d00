<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/psr-http-message-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-exporter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/overtrue/pinyin" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/overtrue/socialite" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/overtrue/wechat" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-captcha" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-helper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-installer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-queue" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/evenement/evenement" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/maennchen/zipstream-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/markbaker/complex" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/markbaker/matrix" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpoffice/phpspreadsheet" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/php-ffmpeg/php-ffmpeg" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/txthinking/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/karsonzhang/fastadmin-addons" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/easywechat-composer/easywechat-composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/cccyun/wechatpay-sdk" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ezyang/htmlpurifier" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nelexa/zip" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/pimple/pimple" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alchemy/binary-driver" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/myclabs/php-enum" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/neutron/temporary-filesystem" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/cache-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/filesystem" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php73" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/process" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>