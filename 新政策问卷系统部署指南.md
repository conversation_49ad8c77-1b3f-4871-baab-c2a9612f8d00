# 新政策问卷系统部署指南

## 系统概述

新的政策问卷系统支持**选项组合关联政策**的逻辑，实现了：
- 问题A的选项1 + 问题B的选项2 → 关联政策A
- 问题A的选项2 + 问题B的选项1 → 关联政策B
- 前端用户答题后，根据选项组合返回匹配的政策列表

## 核心业务逻辑

### 1. 数据结构
- **政策规则表** (`fa_policy_rule`): 存储规则基本信息和关联的政策ID列表
- **政策规则条件表** (`fa_policy_rule_condition`): 存储每个规则的具体条件（选项组合）
- **用户答题记录表** (`fa_policy_user_answer`): 存储用户的答题记录
- **问卷结果表** (`fa_policy_questionnaire_result`): 存储问卷完成后的结果和推荐政策

### 2. 条件类型
- **必须选择** (`must`): 用户必须选择这个选项，规则才能匹配
- **可选** (`optional`): 用户选择任意一个可选条件即可，至少要有一个可选条件匹配
- **排除** (`exclude`): 用户选择了这个选项，规则就不匹配

### 3. 匹配逻辑
1. 获取问卷的所有规则，按权重排序
2. 对每个规则检查用户答案是否满足所有必须条件
3. 检查是否违反排除条件
4. 如果有可选条件，至少要匹配一个
5. 返回匹配的规则及其关联的政策

## 部署步骤

### 1. 数据库部署
```sql
-- 1. 执行数据表结构创建
SOURCE sql/new_policy_questionnaire_structure.sql;

-- 2. 执行演示数据插入
SOURCE sql/new_demo_data.sql;
```

### 2. 文件部署
确保以下文件已正确部署：

#### 模型文件
- `application/admin/model/policy/PolicyRule.php` - 政策规则模型
- `application/admin/model/policy/PolicyRuleCondition.php` - 政策规则条件模型
- 更新现有模型的关联关系

#### 控制器文件
- `application/admin/controller/policy/PolicyRule.php` - 政策规则管理控制器
- `application/api/controller/policy/Questionnaire.php` - 前端API接口（需要手动创建）

#### 视图文件
- `application/admin/view/policy/policyrule/index.html`
- `application/admin/view/policy/policyrule/add.html`
- `application/admin/view/policy/policyrule/edit.html`

#### JS文件
- `public/assets/js/backend/policy/policyrule.js`

### 3. 菜单和权限配置

#### 方法一：执行SQL脚本（推荐）
```sql
-- 执行菜单添加脚本
SOURCE sql/add_policy_rule_menu.sql;

-- 或者执行简化版本
SOURCE sql/quick_add_policy_rule_menu.sql;
```

#### 方法二：手动添加菜单
如果SQL脚本执行失败，可以手动在后台添加：

1. **登录后台管理系统**
2. **进入权限管理 → 菜单规则**
3. **找到"政策问卷"主菜单**，如果不存在则先添加：
   - 规则名称：`policy`
   - 规则标题：`政策问卷`
   - 图标：`fa fa-file-text-o`
   - 类型：菜单
   - 权重：100

4. **在"政策问卷"下添加子菜单**：
   - 规则名称：`policy/policyrule`
   - 规则标题：`政策规则管理`
   - 图标：`fa fa-cogs`
   - 类型：菜单
   - 权重：50

5. **在"政策规则管理"下添加功能权限**：
   - `policy/policyrule/index` - 查看
   - `policy/policyrule/add` - 添加
   - `policy/policyrule/edit` - 编辑
   - `policy/policyrule/del` - 删除
   - `policy/policyrule/multi` - 批量操作
   - `policy/policyrule/getquestions` - 获取问题
   - `policy/policyrule/testmatch` - 测试匹配

6. **为管理员组分配权限**：
   - 进入权限管理 → 管理员分组
   - 编辑超级管理员组
   - 勾选所有政策规则管理相关权限

#### 验证菜单配置
配置完成后，应该能在后台看到以下菜单结构：
```
政策问卷
├── 问卷管理
├── 问题管理
├── 选项管理
├── 政策管理
└── 政策规则管理 ← 新增
```

## 功能测试

### 1. 后台管理测试
1. **规则管理**
   - 访问：后台 → 政策问卷 → 政策规则管理
   - 测试添加规则，设置条件组合
   - 测试编辑规则，修改条件
   - 测试规则匹配功能

2. **条件设置**
   - 选择问卷后自动加载问题
   - 选择问题后自动加载选项
   - 设置不同类型的条件（必须、可选、排除）

### 2. 前端API测试

#### 获取问卷详情
```
GET /api/policy.questionnaire/detail?id=1
```

#### 提交答案
```
POST /api/policy.questionnaire/submit
{
    "questionnaire_id": 1,
    "answers": [
        {
            "question_id": 1,
            "option_ids": [1]
        },
        {
            "question_id": 2,
            "option_ids": [3]
        },
        {
            "question_id": 3,
            "option_ids": [1, 2]
        }
    ]
}
```

#### 获取结果
```
GET /api/policy.questionnaire/result?session_id=xxx
```

### 3. 测试场景

基于演示数据的测试场景：

1. **场景1**: 80岁以上 + 需要经济补贴
   - 预期结果：推荐高龄津贴政策

2. **场景2**: 需要护理 + 需要医疗保障
   - 预期结果：推荐护理补贴 + 医疗优惠政策

3. **场景3**: 60-70岁 + 身体健康 + 需要就业支持
   - 预期结果：推荐再就业政策

4. **场景4**: 70-80岁 + 需要生活服务
   - 预期结果：推荐居家养老服务 + 住房改造政策

5. **场景5**: 任何年龄 + 需要医疗保障
   - 预期结果：推荐医疗保险优惠政策

## 故障排除

### 1. 常见问题
- **关联字段不显示**: 检查formatter函数是否正确设置
- **规则不匹配**: 检查条件设置是否正确，特别是条件类型
- **API返回空结果**: 检查数据库中是否有对应的规则和政策数据

### 2. 调试方法
- 使用政策规则管理中的"测试匹配"功能
- 检查数据库中的规则条件是否正确设置
- 查看API返回的详细错误信息

### 3. 性能优化
- 为常用查询字段添加索引
- 使用缓存存储热门规则
- 优化规则匹配算法

## 扩展功能

### 1. 规则优先级
- 通过权重字段控制规则匹配的优先级
- 高权重规则优先匹配

### 2. 条件组合
- 支持复杂的条件组合逻辑
- 支持条件分组和嵌套

### 3. 统计分析
- 规则匹配统计
- 政策推荐效果分析
- 用户行为分析

## 维护说明

### 1. 数据备份
定期备份政策规则和条件数据

### 2. 规则更新
- 政策变更时及时更新规则
- 定期检查规则的有效性

### 3. 监控告警
- 监控API调用量和响应时间
- 设置规则匹配失败的告警

## 联系支持

如有问题，请联系开发团队或查看相关文档。
