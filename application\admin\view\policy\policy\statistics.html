<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#one" data-toggle="tab">政策统计</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="row">
                    <div class="col-xs-12">
                        <!-- 政策基本统计 -->
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h3 class="panel-title">政策统计概览</h3>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-sm-3">
                                        <div class="info-box bg-aqua">
                                            <span class="info-box-icon"><i class="fa fa-file-text-o"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">政策总数</span>
                                                <span class="info-box-number">{$statistics.total|default=0}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-9">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>政策分类</th>
                                                        <th>数量</th>
                                                        <th>占比</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {volist name="statistics.categories" id="category"}
                                                    <tr>
                                                        <td>{$category.category}</td>
                                                        <td>{$category.count}</td>
                                                        <td>
                                                            {if condition="$statistics.total > 0"}
                                                            {php}echo round(($category['count'] / $statistics['total']) * 100, 1);{/php}%
                                                            {else}
                                                            0%
                                                            {/if}
                                                        </td>
                                                    </tr>
                                                    {/volist}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 热门政策 -->
                        {if condition="isset($popularPolicies) && $popularPolicies"}
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <h4 class="panel-title">热门政策</h4>
                            </div>
                            <div class="panel-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>政策名称</th>
                                                <th>分类</th>
                                                <th>查看次数</th>
                                                <th>推荐次数</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {volist name="popularPolicies" id="policy"}
                                            <tr>
                                                <td>{$policy.title}</td>
                                                <td>{$policy.category}</td>
                                                <td>{$policy.view_count}</td>
                                                <td>{$policy.recommend_count|default=0}</td>
                                            </tr>
                                            {/volist}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        {/if}

                        <!-- 操作按钮 -->
                        <div class="form-group text-center">
                            <button type="button" class="btn btn-primary" onclick="window.close();">关闭统计</button>
                            <button type="button" class="btn btn-success" onclick="window.print();">打印报告</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
