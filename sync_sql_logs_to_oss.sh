#!/bin/bash

# ==============================================================================
# Script Name: sync_sql_logs_to_oss.sh
# Description: This script syncs the previous day's SQL log file from the
#              application's runtime directory to a specified OSS mount directory.
# Author:      Shenma
# Date:        $(date +%Y-%m-%d)
# ==============================================================================

# --- Configuration ---
# Source directory where the SQL logs are stored
SOURCE_DIR="/www/wwwroot/service.jiaqingfu.com.cn/runtime/log/sql/"

# Destination directory (the OSS mount point)
DEST_DIR="/www/wwwroot/service.jiaqingfu.com.cn/public/uploads/uploads/log/sql/"

# --- Script Logic ---

echo "Starting SQL log sync script..."

# 1. Calculate yesterday's date in YYYYMMDD format.
#    Using `date -d "yesterday"` which is standard on most Linux systems.
YESTERDAY_DATE=$(date -d "yesterday" +%Y-%m-%d)
echo "Calculated yesterday's date as: ${YESTERDAY_DATE}"

# 2. Define the source log file name and its full path.
LOG_FILE_NAME="${YESTERDAY_DATE}.log"
SOURCE_FILE="${SOURCE_DIR}${LOG_FILE_NAME}"

# 3. Check if the destination directory exists. If not, create it.
#    The '-p' flag ensures that parent directories are created if needed,
#    and it doesn't return an error if the directory already exists.
if [ ! -d "$DEST_DIR" ]; then
    echo "Destination directory does not exist. Creating it: ${DEST_DIR}"
    mkdir -p "$DEST_DIR"
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to create destination directory. Please check permissions. Exiting."
        exit 1
    fi
else
    echo "Destination directory already exists."
fi

# 4. Check if the source log file for yesterday exists and then copy it.
if [ -f "$SOURCE_FILE" ]; then
    echo "Found log file for yesterday: ${SOURCE_FILE}"
    echo "Copying to destination: ${DEST_DIR}"
    cp "${SOURCE_FILE}" "${DEST_DIR}"
    
    # Verify that the copy was successful
    if [ $? -eq 0 ]; then
        echo "SUCCESS: Successfully copied ${LOG_FILE_NAME} to ${DEST_DIR}"
    else
        echo "ERROR: Failed to copy the log file. Please check file permissions and disk space."
        exit 1
    fi
else
    echo "WARNING: Log file for yesterday (${LOG_FILE_NAME}) not found in ${SOURCE_DIR}"
fi

echo "Sync script finished."
exit 0