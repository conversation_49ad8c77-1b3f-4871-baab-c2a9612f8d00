<?php

namespace app\admin\controller\policy;

use app\common\controller\Backend;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 政策问卷管理
 *
 * @icon fa fa-list-alt
 */
class Questionnaire extends Backend
{
    /**
     * Questionnaire模型对象
     * @var \app\admin\model\policy\Questionnaire
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\policy\Questionnaire;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            
            $list = $this->model
                ->with(['questions'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','title','description','cover_image','status','sort','start_time','end_time','createtime']);
                $row->visible(['status_text','start_time_text','end_time_text']);
                // 添加问题数量
                $row->question_count = $row->questions ? count($row->questions) : 0;
                // 隐藏关联数据的详细信息
                if ($row->questions) {
                    foreach ($row->questions as $question) {
                        $question->visible([]);
                    }
                }
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 问卷统计
     */
    public function statistics($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 获取问卷统计信息
        $statistics = $row->getStatistics($ids);
        
        // 获取问卷结果统计
        $resultStats = \app\admin\model\policy\QuestionnaireResult::getQuestionnaireStats($ids);
        
        // 获取热门政策推荐
        $popularPolicies = \app\admin\model\policy\QuestionnaireResult::getPopularPolicyStats($ids, 10);

        $this->view->assign("row", $row);
        $this->view->assign("statistics", $statistics);
        $this->view->assign("resultStats", $resultStats);
        $this->view->assign("popularPolicies", $popularPolicies);
        
        return $this->view->fetch();
    }

    /**
     * 复制问卷
     */
    public function copy($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if ($this->request->isPost()) {
            $title = $this->request->post('title');
            if (empty($title)) {
                $this->error('问卷标题不能为空');
            }

            Db::startTrans();
            try {
                // 复制问卷
                $newQuestionnaire = $row->toArray();
                unset($newQuestionnaire['id'], $newQuestionnaire['createtime'], $newQuestionnaire['updatetime']);
                $newQuestionnaire['title'] = $title;
                $newQuestionnaire['status'] = 'hidden'; // 默认隐藏状态
                
                $newQuestionnaireModel = new \app\admin\model\policy\Questionnaire();
                $newQuestionnaireModel->save($newQuestionnaire);
                $newQuestionnaireId = $newQuestionnaireModel->id;

                // 复制问题和选项
                $questions = \app\admin\model\policy\Question::where('questionnaire_id', $ids)->select();
                foreach ($questions as $question) {
                    $newQuestionId = $question->copyToQuestionnaire($question->id, $newQuestionnaireId);
                }

                Db::commit();
                $this->success('问卷复制成功', null, ['questionnaire_id' => $newQuestionnaireId]);
            } catch (\Exception $e) {
                Db::rollback();
                $this->error('复制失败：' . $e->getMessage());
            }
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 预览问卷
     */
    public function preview($ids = null)
    {
        $questionnaire = $this->model->getDetailWithQuestions($ids);
        if (!$questionnaire) {
            $this->error(__('No Results were found'));
        }

        $this->view->assign("questionnaire", $questionnaire);
        return $this->view->fetch();
    }
}
