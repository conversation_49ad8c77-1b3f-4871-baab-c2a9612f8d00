<?php

namespace app\admin\controller\policy;

use app\common\controller\Backend;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 政策管理
 *
 * @icon fa fa-file-text-o
 */
class Policy extends Backend
{
    /**
     * Policy模型对象
     * @var \app\admin\model\policy\Policy
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\policy\Policy;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("categoryList", $this->model->getCategoryList());
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            
            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','title','summary','category','tags','cover_image','source','publish_date','effective_date','status','sort','view_count','createtime']);
                $row->visible(['status_text','publish_date_text','effective_date_text','tags_array']);
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 政策详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 增加查看次数
        $row->incrementViewCount($ids);

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 政策统计
     */
    public function statistics()
    {
        // 获取政策统计信息
        $statistics = $this->model->getPolicyStatistics();
        
        // 获取热门政策
        $hotPolicies = $this->model->getHotPolicies(10);
        
        // 获取最新政策
        $latestPolicies = $this->model->getLatestPolicies(10);

        $this->view->assign("statistics", $statistics);
        $this->view->assign("hotPolicies", $hotPolicies);
        $this->view->assign("latestPolicies", $latestPolicies);
        
        return $this->view->fetch();
    }

    /**
     * 政策搜索
     */
    public function search()
    {
        if ($this->request->isPost()) {
            $keyword = $this->request->post('keyword');
            $category = $this->request->post('category');
            $limit = $this->request->post('limit', 20);
            
            $policies = $this->model->searchPolicies($keyword, $category, $limit);
            
            $this->success('搜索成功', $policies);
        }
        
        return $this->view->fetch();
    }

    /**
     * 批量导入政策
     */
    public function import()
    {
        if ($this->request->isPost()) {
            $file = $this->request->file('file');
            if (!$file) {
                $this->error('请选择要导入的文件');
            }

            try {
                // 这里可以根据需要实现Excel或CSV文件的导入逻辑
                // 示例代码，需要根据实际文件格式调整
                
                $this->success('导入成功');
            } catch (\Exception $e) {
                $this->error('导入失败：' . $e->getMessage());
            }
        }
        
        return $this->view->fetch();
    }

    /**
     * 批量导出政策
     */
    public function export()
    {
        $ids = $this->request->param('ids');
        
        if (empty($ids)) {
            $this->error('请选择要导出的政策');
        }

        try {
            $policies = $this->model->where('id', 'in', $ids)->select();
            
            // 这里可以根据需要实现Excel或CSV文件的导出逻辑
            // 示例代码，需要根据实际需求调整
            
            $this->success('导出成功');
        } catch (\Exception $e) {
            $this->error('导出失败：' . $e->getMessage());
        }
    }

    /**
     * 复制政策
     */
    public function copy($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if ($this->request->isPost()) {
            $title = $this->request->post('title');
            if (empty($title)) {
                $this->error('政策标题不能为空');
            }

            try {
                // 复制政策
                $newPolicy = $row->toArray();
                unset($newPolicy['id'], $newPolicy['createtime'], $newPolicy['updatetime']);
                $newPolicy['title'] = $title;
                $newPolicy['status'] = 'hidden'; // 默认隐藏状态
                $newPolicy['view_count'] = 0; // 重置查看次数
                
                $newPolicyModel = new \app\admin\model\policy\Policy();
                $newPolicyModel->save($newPolicy);
                
                $this->success('政策复制成功', null, ['policy_id' => $newPolicyModel->id]);
            } catch (\Exception $e) {
                $this->error('复制失败：' . $e->getMessage());
            }
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
}
