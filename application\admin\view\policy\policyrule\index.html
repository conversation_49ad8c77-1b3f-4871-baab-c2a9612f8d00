<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="status">
            <li class="{:$Think.get.status === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">全部</a></li>
            <li class="{:$Think.get.status === 'normal' ? 'active' : ''}"><a href="#t-normal" data-value="normal" data-toggle="tab">正常</a></li>
            <li class="{:$Think.get.status === 'hidden' ? 'active' : ''}"><a href="#t-hidden" data-value="hidden" data-toggle="tab">隐藏</a></li>
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="刷新" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('policy/policyrule/add')?'':'hide'}" title="添加" ><i class="fa fa-plus"></i> 添加</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('policy/policyrule/edit')?'':'hide'}" title="编辑" ><i class="fa fa-pencil"></i> 编辑</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('policy/policyrule/del')?'':'hide'}" title="删除" ><i class="fa fa-trash"></i> 删除</a>
                        <a href="javascript:;" class="btn btn-info btn-test btn-disabled disabled {:$auth->check('policy/policyrule/testmatch')?'':'hide'}" title="测试匹配" ><i class="fa fa-flask"></i> 测试匹配</a>
                        
                        <div class="dropdown btn-group {:$auth->check('policy/policyrule/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> 更多</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> 设为正常</a></li>
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> 设为隐藏</a></li>
                            </ul>
                        </div>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('policy/policyrule/edit')}"
                           data-operate-del="{:$auth->check('policy/policyrule/del')}"
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
