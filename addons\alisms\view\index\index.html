<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/>
    <title>阿里云短信发送示例 - {$site.name}</title>

    <link href="__CDN__/assets/libs/bootstrap/dist/css/bootstrap.min.css" rel="stylesheet">

    <!--[if lt IE 9]>
    <script src="/assets/js/html5shiv.js"></script>
    <script src="/assets/js/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<div class="container">
    <div class="well" style="margin-top:30px;">
        <div class="alert alert-danger">温馨提示：仅用于测试插件是否能正常发送短信</div>
        <form class="form-horizontal" action="{:addon_url('alisms/index/send')}" method="POST">
            <fieldset>
                <legend style="padding-bottom:15px;">阿里云短信发送测试</legend>
                <div class="form-group">
                    <label class="col-lg-2 control-label">手机号</label>
                    <div class="col-lg-10">
                        <input type="text" class="form-control" name="mobile" placeholder="手机号">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-lg-2 control-label">消息模板</label>
                    <div class="col-lg-10">
                        <select name="template" class="form-control">
                            {foreach name="templateList" id="item"}
                            <option value="{$key}">{$item} ({$key})</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-lg-10 col-lg-offset-2">
                        <button type="submit" class="btn btn-primary">发送</button>
                        <button type="reset" class="btn btn-default">重置</button>
                    </div>
                </div>
            </fieldset>
        </form>
    </div>
</div>

<script src="__CDN__/assets/libs/jquery/dist/jquery.min.js"></script>
<script src="__CDN__/assets/libs/bootstrap/dist/js/bootstrap.min.js"></script>

<script type="text/javascript">
    $(function () {

    });
</script>
</body>
</html>
