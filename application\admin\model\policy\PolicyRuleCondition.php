<?php

namespace app\admin\model\policy;

use think\Model;

/**
 * 政策规则条件模型
 */
class PolicyRuleCondition extends Model
{
    // 表名
    protected $name = 'policy_rule_condition';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'condition_type_text'
    ];

    /**
     * 条件类型列表
     */
    public function getConditionTypeList()
    {
        return [
            'must' => '必须选择',
            'optional' => '可选',
            'exclude' => '排除'
        ];
    }

    /**
     * 条件类型文本获取器
     */
    public function getConditionTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['condition_type']) ? $data['condition_type'] : '');
        $list = $this->getConditionTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 关联规则
     */
    public function rule()
    {
        return $this->belongsTo('PolicyRule', 'rule_id', 'id');
    }

    /**
     * 关联问题
     */
    public function question()
    {
        return $this->belongsTo('app\admin\model\policy\Question', 'question_id', 'id');
    }

    /**
     * 关联答案选项
     */
    public function answerOption()
    {
        return $this->belongsTo('app\admin\model\policy\AnswerOption', 'answer_option_id', 'id');
    }

    /**
     * 批量添加条件
     * @param int $ruleId 规则ID
     * @param array $conditions 条件数组
     * @return bool
     */
    public function addConditions($ruleId, $conditions)
    {
        // 先删除原有条件
        $this->where('rule_id', $ruleId)->delete();
        
        // 添加新条件
        $data = [];
        foreach ($conditions as $condition) {
            $data[] = [
                'rule_id' => $ruleId,
                'question_id' => $condition['question_id'],
                'answer_option_id' => $condition['answer_option_id'],
                'condition_type' => $condition['condition_type'] ?? 'must',
                'createtime' => time()
            ];
        }
        
        if (!empty($data)) {
            return $this->insertAll($data);
        }
        
        return true;
    }

    /**
     * 获取规则的条件组合描述
     * @param int $ruleId 规则ID
     * @return string
     */
    public function getConditionDescription($ruleId)
    {
        $conditions = $this->with(['question', 'answerOption'])
            ->where('rule_id', $ruleId)
            ->order('question_id', 'asc')
            ->select();
        
        $descriptions = [];
        $groupedConditions = [];
        
        // 按问题分组
        foreach ($conditions as $condition) {
            $questionId = $condition->question_id;
            if (!isset($groupedConditions[$questionId])) {
                $groupedConditions[$questionId] = [
                    'question' => $condition->question,
                    'conditions' => []
                ];
            }
            $groupedConditions[$questionId]['conditions'][] = $condition;
        }
        
        // 生成描述
        foreach ($groupedConditions as $group) {
            $questionTitle = $group['question'] ? $group['question']->title : '未知问题';
            $optionTexts = [];
            
            foreach ($group['conditions'] as $condition) {
                $optionTitle = $condition->answerOption ? $condition->answerOption->title : '未知选项';
                $typeText = $condition->condition_type_text;
                $optionTexts[] = "{$optionTitle}({$typeText})";
            }
            
            $descriptions[] = "{$questionTitle}: " . implode('、', $optionTexts);
        }
        
        return implode(' + ', $descriptions);
    }

    /**
     * 验证条件组合是否有效
     * @param array $conditions 条件数组
     * @return array [valid => bool, message => string]
     */
    public function validateConditions($conditions)
    {
        if (empty($conditions)) {
            return ['valid' => false, 'message' => '至少需要一个条件'];
        }
        
        $questionIds = [];
        foreach ($conditions as $condition) {
            if (empty($condition['question_id']) || empty($condition['answer_option_id'])) {
                return ['valid' => false, 'message' => '问题ID和选项ID不能为空'];
            }
            
            // 检查同一问题是否有冲突的条件
            $questionId = $condition['question_id'];
            if (!isset($questionIds[$questionId])) {
                $questionIds[$questionId] = [];
            }
            $questionIds[$questionId][] = $condition;
        }
        
        // 检查每个问题的条件是否合理
        foreach ($questionIds as $questionId => $questionConditions) {
            $mustCount = 0;
            $excludeCount = 0;
            
            foreach ($questionConditions as $condition) {
                if ($condition['condition_type'] === 'must') {
                    $mustCount++;
                } elseif ($condition['condition_type'] === 'exclude') {
                    $excludeCount++;
                }
            }
            
            // 同一问题不能同时有多个必须条件（单选题）
            if ($mustCount > 1) {
                $question = Question::find($questionId);
                if ($question && $question->type === 'single') {
                    return ['valid' => false, 'message' => "单选题「{$question->title}」不能有多个必须条件"];
                }
            }
        }
        
        return ['valid' => true, 'message' => ''];
    }
}
