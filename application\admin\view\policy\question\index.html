<div class="panel panel-default panel-intro">
    
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="type">
            <li class="{:$Think.get.type === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">全部</a></li>
            <li class="{:$Think.get.type === 'single' ? 'active' : ''}"><a href="#t-single" data-value="single" data-toggle="tab">单选题</a></li>
            <li class="{:$Think.get.type === 'multiple' ? 'active' : ''}"><a href="#t-multiple" data-value="multiple" data-toggle="tab">多选题</a></li>
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="刷新" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('policy/question/add')?'':'hide'}" title="添加" ><i class="fa fa-plus"></i> 添加</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('policy/question/edit')?'':'hide'}" title="编辑" ><i class="fa fa-pencil"></i> 编辑</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('policy/question/del')?'':'hide'}" title="删除" ><i class="fa fa-trash"></i> 删除</a>
                        <a href="javascript:;" class="btn btn-info btn-options btn-disabled disabled {:$auth->check('policy/question/options')?'':'hide'}" title="管理选项" ><i class="fa fa-list"></i> 管理选项</a>
                        <a href="javascript:;" class="btn btn-warning btn-statistics btn-disabled disabled {:$auth->check('policy/question/statistics')?'':'hide'}" title="查看统计" ><i class="fa fa-bar-chart"></i> 统计</a>
                        <a href="javascript:;" class="btn btn-primary btn-copy btn-disabled disabled {:$auth->check('policy/question/copy')?'':'hide'}" title="复制问题" ><i class="fa fa-copy"></i> 复制</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
