<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">所属问卷:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-questionnaire_id" data-rule="required" class="form-control selectpicker" name="row[questionnaire_id]">
                <option value="">请选择问卷</option>
                {foreach name="questionnaireList" item="vo"}
                    <option value="{$vo.id}" {in name="vo.id" value="$row.questionnaire_id"}selected{/in}>{$vo.title}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}" placeholder="请输入问题标题">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Description')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-description" class="form-control" rows="3" name="row[description]" cols="50" placeholder="请输入问题描述（可选）">{$row.description|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-type" data-rule="required" class="form-control selectpicker" name="row[type]">
                {foreach name="typeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">是否必答:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-is_required" class="form-control selectpicker" name="row[is_required]">
                <option value="0" {in name="0" value="$row.is_required"}selected{/in}>否</option>
                <option value="1" {in name="1" value="$row.is_required"}selected{/in}>是</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">排序权重:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sort" class="form-control" name="row[sort]" type="number" value="{$row.sort}" placeholder="数值越大排序越靠前">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-status" data-rule="required" class="form-control selectpicker" name="row[status]">
                {foreach name="statusList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.status"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
