# 政策问卷功能部署和测试指南

## 1. 部署步骤

### 1.1 数据库部署
1. 确保数据库表已创建（用户已完成）
2. 执行菜单权限配置：
   ```sql
   -- 执行快速菜单配置（已根据实际表结构修正）
   source sql/quick_add_policy_menu.sql;
   ```
3. 清除系统缓存以加载新菜单

### 1.2 文件部署检查
确认以下文件已正确部署：

#### 模型文件
- `application/admin/model/policy/Questionnaire.php`
- `application/admin/model/policy/Question.php`
- `application/admin/model/policy/AnswerOption.php`
- `application/admin/model/policy/Policy.php`
- `application/admin/model/policy/UserAnswer.php`
- `application/admin/model/policy/QuestionnaireResult.php`

#### 控制器文件
- `application/admin/controller/policy/Questionnaire.php`
- `application/admin/controller/policy/Question.php`
- `application/admin/controller/policy/AnswerOption.php`
- `application/admin/controller/policy/Policy.php`
- `application/api/controller/policy/Questionnaire.php`
- `application/api/controller/policy/Policy.php`

#### 服务类文件
- `application/common/service/PolicyQuestionnaireService.php`

#### 视图文件
- `application/admin/view/policy/questionnaire/index.html`
- `application/admin/view/policy/questionnaire/add.html`
- `application/admin/view/policy/questionnaire/edit.html`
- `application/admin/view/policy/question/index.html`

#### JavaScript文件
- `public/assets/js/backend/policy/questionnaire.js`
- `public/assets/js/backend/policy/question.js`

## 2. 功能测试清单

### 2.1 后台管理测试

#### 问卷管理测试
- [ ] 问卷列表显示正常
- [ ] 添加问卷功能正常
- [ ] 编辑问卷功能正常
- [ ] 删除问卷功能正常
- [ ] 问卷状态切换正常
- [ ] 问卷复制功能正常
- [ ] 问卷统计功能正常
- [ ] 问卷预览功能正常

#### 问题管理测试
- [ ] 问题列表显示正常
- [ ] 添加问题功能正常（单选/多选）
- [ ] 编辑问题功能正常
- [ ] 删除问题功能正常
- [ ] 问题排序功能正常
- [ ] 管理答案选项功能正常
- [ ] 问题统计功能正常
- [ ] 复制问题功能正常

#### 答案选项管理测试
- [ ] 选项列表显示正常
- [ ] 添加选项功能正常
- [ ] 编辑选项功能正常
- [ ] 删除选项功能正常
- [ ] 政策关联功能正常
- [ ] 权重设置功能正常
- [ ] 选项统计功能正常

#### 政策管理测试
- [ ] 政策列表显示正常
- [ ] 添加政策功能正常
- [ ] 编辑政策功能正常
- [ ] 删除政策功能正常
- [ ] 政策分类功能正常
- [ ] 政策搜索功能正常
- [ ] 政策统计功能正常

### 2.2 API接口测试

#### 问卷API测试
```bash
# 获取问卷列表
GET /api/policy.questionnaire/lists

# 获取问卷详情
GET /api/policy.questionnaire/detail?id=1

# 提交问卷答案（需要登录）
POST /api/policy.questionnaire/submit
{
    "questionnaire_id": 1,
    "answers": {
        "1": [1, 2],
        "2": [3]
    },
    "start_time": 1640995200
}

# 获取问卷结果（需要登录）
GET /api/policy.questionnaire/result?questionnaire_id=1

# 获取答题记录（需要登录）
GET /api/policy.questionnaire/answers?questionnaire_id=1

# 重新答题（需要登录）
POST /api/policy.questionnaire/reset
{
    "questionnaire_id": 1
}

# 获取问卷统计
GET /api/policy.questionnaire/statistics?questionnaire_id=1
```

#### 政策API测试
```bash
# 获取政策列表
GET /api/policy.policy/lists

# 获取政策详情
GET /api/policy.policy/detail?id=1

# 搜索政策
GET /api/policy.policy/search?keyword=养老&category=elderly_care

# 获取热门政策
GET /api/policy.policy/hot?limit=10

# 获取最新政策
GET /api/policy.policy/latest?limit=10

# 获取政策分类
GET /api/policy.policy/categories

# 获取推荐政策
POST /api/policy.policy/recommend
{
    "answer_option_ids": [1, 2, 3],
    "limit": 10
}

# 获取相关政策
GET /api/policy.policy/related?id=1&limit=5
```

## 3. 性能优化建议

### 3.1 数据库优化
1. 确保所有外键字段都有索引
2. 对经常查询的字段添加索引
3. 定期清理软删除的数据
4. 考虑对大表进行分区

### 3.2 缓存优化
1. 对热门政策进行缓存
2. 对问卷详情进行缓存
3. 对政策分类进行缓存
4. 对统计数据进行缓存

### 3.3 代码优化
1. 使用批量操作减少数据库查询
2. 优化N+1查询问题
3. 使用合适的数据结构
4. 添加适当的错误处理

## 4. 常见问题排查

### 4.1 菜单不显示
- 检查菜单权限是否正确配置
- 检查用户组权限是否包含相关权限
- 清除缓存后重试

### 4.2 API接口报错
- 检查路由配置是否正确
- 检查控制器文件是否存在
- 检查数据库连接是否正常
- 查看错误日志获取详细信息

### 4.3 数据保存失败
- 检查数据表结构是否正确
- 检查字段长度限制
- 检查必填字段是否有值
- 检查数据验证规则

### 4.4 推荐算法不准确
- 检查答案选项与政策的关联关系
- 检查权重设置是否合理
- 调整推荐算法参数
- 增加更多测试数据

## 5. 监控和维护

### 5.1 日常监控
- 监控API接口响应时间
- 监控数据库查询性能
- 监控用户答题完成率
- 监控政策推荐准确率

### 5.2 定期维护
- 定期备份数据库
- 定期清理日志文件
- 定期更新政策内容
- 定期优化数据库索引

## 6. 扩展功能建议

### 6.1 短期扩展
- 添加问卷模板功能
- 添加批量导入政策功能
- 添加问卷分享功能
- 添加答题时间限制

### 6.2 长期扩展
- 添加智能推荐算法
- 添加用户画像分析
- 添加政策更新提醒
- 添加多语言支持

## 7. 部署完成验证

部署完成后，请按以下步骤验证：

1. 登录后台管理系统
2. 检查"政策问卷"菜单是否显示
3. 创建一个测试问卷
4. 添加测试问题和选项
5. 关联测试政策
6. 通过API接口测试问卷功能
7. 验证推荐算法是否正常工作

如果所有测试通过，说明政策问卷功能部署成功！
