<?php

return [
    [
        'name' => 'asteriskposition',
        'title' => '*号位置',
        'type' => 'radio',
        'group' => '',
        'visible' => '',
        'content' => [
            'before' => '位于文本前',
            'after' => '位于文本后',
        ],
        'value' => 'before',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'offset',
        'title' => '弹窗位置',
        'type' => 'radio',
        'content' => [
            'auto' => '居中',
            't' => '顶部',
            'b' => '底部',
            'l' => '左部',
            'r' => '右部',
        ],
        'value' => 'r',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'anim',
        'title' => '打开动画',
        'type' => 'select',
        'content' => [
            '平滑放大',
            '从上掉落',
            '从最底部往上滑入',
            '从左滑入',
            '从左翻滚',
            '渐显',
            '抖动',
            'slideDown' => '从上边缘往下',
            'slideLeft' => '从右边缘往左',
            'slideUp' => '从下边缘往上',
            'slideRight' => '从左边缘往右',
        ],
        'value' => 'slideLeft',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'area',
        'title' => '弹窗宽高',
        'type' => 'string',
        'content' => [],
        'value' => '["60%", "100%"]',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'shade',
        'title' => '阴影透明度',
        'type' => 'number',
        'content' => [],
        'value' => '0.3',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'shadeClose',
        'title' => '点击阴影关闭弹窗',
        'type' => 'bool',
        'content' => [
            1 => '开启',
            0 => '关闭',
        ],
        'value' => '1',
        'rule' => 'required',
        'msg' => '',
        'tip' => '',
        'ok' => '',
        'extend' => '',
    ],
];
