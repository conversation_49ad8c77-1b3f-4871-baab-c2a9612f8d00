<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#one" data-toggle="tab">问卷预览</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="row">
                    <div class="col-xs-12">
                        <!-- 问卷基本信息 -->
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h3 class="panel-title">{$questionnaire.title}</h3>
                            </div>
                            <div class="panel-body">
                                <p class="text-muted">{$questionnaire.description}</p>
                                {if condition="$questionnaire.cover_image"}
                                <div class="form-group">
                                    <img src="{$questionnaire.cover_image}" class="img-responsive" style="max-width: 300px;">
                                </div>
                                {/if}
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>开始时间：</strong>{$questionnaire.start_time_text|default='无限制'}
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>结束时间：</strong>{$questionnaire.end_time_text|default='无限制'}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 问题列表 -->
                        {volist name="questionnaire.questions" id="question"}
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    问题 {$i}：{$question.title}
                                    {if condition="$question.is_required"}
                                    <span class="text-danger">*</span>
                                    {/if}
                                </h4>
                            </div>
                            <div class="panel-body">
                                {if condition="$question.description"}
                                <p class="text-muted">{$question.description}</p>
                                {/if}
                                
                                <div class="form-group">
                                    {if condition="$question.type == 'single'"}
                                    <!-- 单选题 -->
                                    {volist name="question.answerOptions" id="option"}
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="question_{$question.id}" value="{$option.id}" disabled>
                                            {$option.title}
                                        </label>
                                        {if condition="$option.description"}
                                        <small class="text-muted d-block">{$option.description}</small>
                                        {/if}
                                    </div>
                                    {/volist}
                                    {elseif condition="$question.type == 'multiple'"}
                                    <!-- 多选题 -->
                                    {volist name="question.answerOptions" id="option"}
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="question_{$question.id}[]" value="{$option.id}" disabled>
                                            {$option.title}
                                        </label>
                                        {if condition="$option.description"}
                                        <small class="text-muted d-block">{$option.description}</small>
                                        {/if}
                                    </div>
                                    {/volist}
                                    {/if}
                                </div>
                            </div>
                        </div>
                        {/volist}

                        <!-- 操作按钮 -->
                        <div class="form-group text-center">
                            <button type="button" class="btn btn-primary" onclick="window.close();">关闭预览</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
