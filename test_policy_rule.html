<!DOCTYPE html>
<html>
<head>
    <title>政策规则管理测试</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/bootstrap/3.4.1/css/bootstrap.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/3.4.1/js/bootstrap.min.js"></script>
</head>
<body>
<div class="container" style="margin-top: 20px;">
    <h2>政策规则管理功能测试</h2>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">测试步骤</h3>
        </div>
        <div class="panel-body">
            <ol>
                <li><strong>执行数据库脚本</strong>：
                    <pre>SOURCE sql/new_policy_questionnaire_structure.sql;
SOURCE sql/new_demo_data.sql;
SOURCE sql/quick_add_policy_rule_menu.sql;</pre>
                </li>
                
                <li><strong>检查菜单</strong>：
                    <pre>SOURCE sql/check_policy_rule_menu.sql;</pre>
                </li>
                
                <li><strong>访问后台</strong>：
                    <ul>
                        <li>登录后台管理系统</li>
                        <li>进入：政策问卷 → 政策规则管理</li>
                        <li>如果看不到菜单，清除浏览器缓存并重新登录</li>
                    </ul>
                </li>
                
                <li><strong>测试添加规则</strong>：
                    <ul>
                        <li>点击"添加"按钮</li>
                        <li>选择问卷：老年人政策需求评估问卷</li>
                        <li>填写规则名称和描述</li>
                        <li>选择关联政策</li>
                        <li>添加条件组合</li>
                        <li>保存规则</li>
                    </ul>
                </li>
                
                <li><strong>测试编辑规则</strong>：
                    <ul>
                        <li>选择已有规则，点击"编辑"</li>
                        <li>修改条件组合</li>
                        <li>保存修改</li>
                    </ul>
                </li>
                
                <li><strong>测试规则匹配</strong>：
                    <ul>
                        <li>选择规则，点击"测试匹配"</li>
                        <li>模拟用户答题</li>
                        <li>查看匹配结果</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">预设的测试场景</h3>
        </div>
        <div class="panel-body">
            <div class="row">
                <div class="col-md-6">
                    <h4>场景1：高龄津贴</h4>
                    <ul>
                        <li>年龄段：80岁以上</li>
                        <li>政策需求：经济补贴</li>
                        <li>预期结果：推荐高龄津贴政策</li>
                    </ul>
                    
                    <h4>场景2：护理医疗</h4>
                    <ul>
                        <li>健康状况：需要护理</li>
                        <li>政策需求：医疗保障</li>
                        <li>预期结果：推荐护理补贴 + 医疗优惠</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4>场景3：再就业</h4>
                    <ul>
                        <li>年龄段：60-70岁</li>
                        <li>健康状况：身体健康</li>
                        <li>政策需求：就业支持</li>
                        <li>预期结果：推荐再就业政策</li>
                    </ul>
                    
                    <h4>场景4：生活服务</h4>
                    <ul>
                        <li>年龄段：70-80岁</li>
                        <li>政策需求：生活服务</li>
                        <li>预期结果：推荐居家养老 + 住房改造</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">常见问题排查</h3>
        </div>
        <div class="panel-body">
            <h4>1. 看不到政策规则管理菜单</h4>
            <ul>
                <li>检查是否执行了菜单添加SQL脚本</li>
                <li>确认当前用户有相应权限</li>
                <li>清除浏览器缓存并重新登录</li>
                <li>执行检查脚本：<code>SOURCE sql/check_policy_rule_menu.sql;</code></li>
            </ul>
            
            <h4>2. 添加规则时看不到问题选项</h4>
            <ul>
                <li>确认选择的问卷中有问题</li>
                <li>确认问题的状态为"正常"</li>
                <li>确认问题下有答案选项且状态为"正常"</li>
                <li>检查浏览器控制台是否有JavaScript错误</li>
            </ul>
            
            <h4>3. 条件不能正确显示</h4>
            <ul>
                <li>检查浏览器控制台的JavaScript错误</li>
                <li>确认问卷数据完整</li>
                <li>刷新页面重试</li>
            </ul>
            
            <h4>4. 规则不匹配</h4>
            <ul>
                <li>检查条件设置是否正确</li>
                <li>使用"测试匹配"功能验证</li>
                <li>确认用户答案与条件设置一致</li>
                <li>检查条件类型设置（必须/可选/排除）</li>
            </ul>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">API测试</h3>
        </div>
        <div class="panel-body">
            <h4>前端API测试地址：</h4>
            <ul>
                <li><strong>获取问卷详情</strong>：<code>GET /api/policy.questionnaire/detail?id=1</code></li>
                <li><strong>提交答案</strong>：<code>POST /api/policy.questionnaire/submit</code></li>
                <li><strong>获取结果</strong>：<code>GET /api/policy.questionnaire/result?session_id=xxx</code></li>
            </ul>
            
            <h4>提交答案示例：</h4>
            <pre>{
    "questionnaire_id": 1,
    "answers": [
        {
            "question_id": 1,
            "option_ids": [3]
        },
        {
            "question_id": 2,
            "option_ids": [3]
        },
        {
            "question_id": 3,
            "option_ids": [1, 2]
        }
    ]
}</pre>
        </div>
    </div>
    
    <div class="alert alert-info">
        <strong>提示：</strong>如果遇到问题，请检查：
        <ol>
            <li>数据库表是否正确创建</li>
            <li>演示数据是否正确插入</li>
            <li>菜单权限是否正确配置</li>
            <li>浏览器控制台是否有错误信息</li>
        </ol>
    </div>
</div>
</body>
</html>
