define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'policy/answeroption/index' + location.search,
                    add_url: 'policy/answeroption/add',
                    edit_url: 'policy/answeroption/edit',
                    del_url: 'policy/answeroption/del',
                    multi_url: 'policy/answeroption/multi',
                    import_url: 'policy/answeroption/import',
                    table: 'policy_answer_option',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'sort',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: 'ID', operate: false, sortable: true},
                        {field: 'question.questionnaire.title', title: '所属问卷', operate: 'LIKE', formatter: function(value, row, index) {
                            return row.question && row.question.questionnaire && row.question.questionnaire.title ?
                                row.question.questionnaire.title : '未关联问卷';
                        }},
                        {field: 'question.title', title: '问题标题', operate: 'LIKE', formatter: function(value, row, index) {
                            return row.question && row.question.title ? row.question.title : '未关联问题';
                        }},
                        {field: 'title', title: '选项标题', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'description', title: '选项描述', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'select_count', title: '选择次数', operate: false},
                        {field: 'policy_count', title: '关联政策数', operate: false},
                        {field: 'sort', title: '排序', operate: false, sortable: true},
                        {field: 'status', title: '状态', searchList: {"normal":"正常","hidden":"隐藏"}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: '创建时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime, sortable: true},
                        {field: 'operate', title: '操作', table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
