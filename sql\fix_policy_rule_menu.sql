-- 修复政策规则管理菜单URL路径
-- 将 policy/policyrule 改为 policy/policyrule

-- 1. 更新主菜单路径
UPDATE fa_auth_rule 
SET name = 'policy/policyrule'
WHERE name = 'policy/policy_rule';

-- 2. 更新所有子权限路径
UPDATE fa_auth_rule 
SET name = 'policy/policyrule/index'
WHERE name = 'policy/policy_rule/index';

UPDATE fa_auth_rule 
SET name = 'policy/policyrule/add'
WHERE name = 'policy/policy_rule/add';

UPDATE fa_auth_rule 
SET name = 'policy/policyrule/edit'
WHERE name = 'policy/policy_rule/edit';

UPDATE fa_auth_rule 
SET name = 'policy/policyrule/del'
WHERE name = 'policy/policy_rule/del';

UPDATE fa_auth_rule 
SET name = 'policy/policyrule/multi'
WHERE name = 'policy/policy_rule/multi';

UPDATE fa_auth_rule 
SET name = 'policy/policyrule/getquestions'
WHERE name = 'policy/policy_rule/getquestions';

UPDATE fa_auth_rule 
SET name = 'policy/policyrule/testmatch'
WHERE name = 'policy/policy_rule/testmatch';

-- 3. 显示修复结果
SELECT '=== 菜单路径修复完成 ===' as result;

SELECT 
    name,
    title,
    CASE 
        WHEN pid = 0 THEN '主菜单'
        WHEN ismenu = 1 THEN '子菜单'
        ELSE '功能权限'
    END as type
FROM fa_auth_rule 
WHERE name = 'policy/policyrule' OR name LIKE 'policy/policyrule/%'
ORDER BY pid, weigh DESC;
