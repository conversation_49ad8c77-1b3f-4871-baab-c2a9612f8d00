<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">政策标题:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}" placeholder="请输入政策标题">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">政策摘要:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-summary" class="form-control" rows="3" name="row[summary]" placeholder="请输入政策摘要">{$row.summary|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">政策内容:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" class="form-control editor" rows="5" name="row[content]" placeholder="请输入政策详细内容">{$row.content|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">政策分类:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-category" data-rule="required" class="form-control selectpicker" name="row[category]">
                {foreach name="categoryList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.category"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">政策标签:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tags" class="form-control" name="row[tags]" type="text" value="{$row.tags|htmlentities}" placeholder="请输入政策标签，用逗号分隔">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">封面图片:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-cover_image" class="form-control" size="50" name="row[cover_image]" type="text" value="{$row.cover_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-cover_image" class="btn btn-danger plupload" data-input-id="c-cover_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/webp" data-multiple="false" data-preview-id="p-cover_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-cover_image" class="btn btn-primary fachoose" data-input-id="c-cover_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-cover_image"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-cover_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">政策来源:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-source" class="form-control" name="row[source]" type="text" value="{$row.source|htmlentities}" placeholder="请输入政策来源">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">发布时间:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-publish_date" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[publish_date]" type="text" value="{$row.publish_date_text}" placeholder="请选择发布时间">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">生效时间:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-effective_date" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[effective_date]" type="text" value="{$row.effective_date_text}" placeholder="请选择生效时间">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">排序权重:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sort" class="form-control" name="row[sort]" type="number" value="{$row.sort}" placeholder="数值越大排序越靠前">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-status" data-rule="required" class="form-control selectpicker" name="row[status]">
                {foreach name="statusList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.status"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
