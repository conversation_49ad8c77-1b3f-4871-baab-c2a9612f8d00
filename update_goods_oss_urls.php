<?php
// 设置应用路径
define('APP_PATH', __DIR__ . '/application/');

// 加载ThinkPHP框架启动文件
require __DIR__ . '/thinkphp/start.php';

use think\Db;
use think\Config;

// 旧的OSS地址
$old_oss_url = 'https://jiaqingfu.oss-cn-beijing.aliyuncs.com';

// 从数据库fa_config表中获取新的CDN地址
$new_cdn_url ='https://ylzjstatics.jiaqingfu.com.cn';

if (empty($new_cdn_url) || $new_cdn_url == $old_oss_url) {
    echo "错误: 未能从 `fa_config` 表的 `alioss` 配置中获取到新的有效CDN地址." . PHP_EOL;
    exit;
}

echo "-- [INFO] 开始为 `fa_wanlshop_goods` 表生成URL替换脚本..." . PHP_EOL;
echo "-- [INFO] 旧OSS地址: " . $old_oss_url . PHP_EOL;
echo "-- [INFO] 新CDN地址: " . $new_cdn_url . PHP_EOL;
echo "--------------------------------------------------" . PHP_EOL . PHP_EOL;

// 获取数据库配置
$db_config = Config::get('database');
$table_prefix = $db_config['prefix'];
$table_name = $table_prefix . 'wanlshop_goods';

$generated_sql = [];

// 获取表的所有字段信息
$columns = Db::query("SHOW COLUMNS FROM `{$table_name}`");

foreach ($columns as $column) {
    $field_name = $column['Field'];
    $field_type = strtolower($column['Type']);

    // 只处理文本相关类型字段
    if (strpos($field_type, 'char') !== false || strpos($field_type, 'text') !== false) {
        // 生成UPDATE语句
        $sql = "UPDATE `{$table_name}` SET `{$field_name}` = REPLACE(`{$field_name}`, '{$old_oss_url}', '{$new_cdn_url}') WHERE `{$field_name}` LIKE '%{$old_oss_url}%';";
        $generated_sql[] = $sql;
    }
}

if (empty($generated_sql)) {
    echo "-- [INFO] 在 `{$table_name}` 表中未找到任何需要更新的文本字段." . PHP_EOL;
} else {
    echo "-- [SUCCESS] 已生成以下SQL语句, 请检查后手动执行:" . PHP_EOL . PHP_EOL;
    echo implode(PHP_EOL, $generated_sql);
}

echo PHP_EOL . PHP_EOL . "--------------------------------------------------" . PHP_EOL;
echo "-- [INFO] 脚本执行完毕." . PHP_EOL;

?>