-- 完整修复政策规则管理404问题
-- 执行此脚本解决URL路径问题

-- 1. 删除可能存在的错误菜单项
DELETE FROM fa_auth_rule WHERE name LIKE 'policy/policy_rule%';

-- 2. 确保政策问卷主菜单存在
INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', 0, 'policy', '政策问卷', 'fa fa-file-text-o', '', '政策问卷管理系统', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 'normal');

-- 3. 添加政策规则管理菜单（正确的URL路径）
INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', (SELECT id FROM (SELECT id FROM fa_auth_rule WHERE name = 'policy' LIMIT 1) as temp), 'policy/policyrule', '政策规则管理', 'fa fa-cogs', '', '管理政策推荐规则和条件组合', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 50, 'normal');

-- 4. 添加功能权限
INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', (SELECT id FROM (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1) as temp), 'policy/policyrule/index', '查看', '', '', '查看政策规则列表', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', (SELECT id FROM (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1) as temp), 'policy/policyrule/add', '添加', '', '', '添加新的政策规则', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', (SELECT id FROM (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1) as temp), 'policy/policyrule/edit', '编辑', '', '', '编辑政策规则', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', (SELECT id FROM (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1) as temp), 'policy/policyrule/del', '删除', '', '', '删除政策规则', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', (SELECT id FROM (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1) as temp), 'policy/policyrule/multi', '批量操作', '', '', '批量操作政策规则', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', (SELECT id FROM (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1) as temp), 'policy/policyrule/getquestions', '获取问题', '', '', '根据问卷获取问题列表', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', (SELECT id FROM (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1) as temp), 'policy/policyrule/testmatch', '测试匹配', '', '', '测试规则匹配功能', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal');

-- 5. 为超级管理员添加权限
UPDATE fa_auth_group 
SET rules = CONCAT(IFNULL(rules, ''), ',', (
    SELECT GROUP_CONCAT(id) 
    FROM fa_auth_rule 
    WHERE name = 'policy/policyrule' OR name LIKE 'policy/policyrule/%'
))
WHERE id = 1;

-- 6. 清理重复逗号
UPDATE fa_auth_group 
SET rules = TRIM(BOTH ',' FROM REPLACE(REPLACE(REPLACE(rules, ',,', ','), ',,', ','), ',,', ','))
WHERE id = 1;

-- 7. 显示修复结果
SELECT '=== 政策规则管理404问题修复完成 ===' as result;

-- 显示菜单结构
SELECT 
    r.id,
    r.pid,
    r.name,
    r.title,
    r.icon,
    r.ismenu,
    r.weigh,
    r.status,
    CASE 
        WHEN r.pid = 0 THEN '📁 主菜单'
        WHEN r.ismenu = 1 THEN '📂 子菜单'
        ELSE '⚙️ 功能权限'
    END as menu_type
FROM fa_auth_rule r 
WHERE r.name = 'policy/policyrule' 
   OR r.name LIKE 'policy/policyrule/%'
ORDER BY r.pid, r.weigh DESC, r.id;

-- 8. 验证URL路径
SELECT '=== URL路径验证 ===' as info;
SELECT
    '控制器文件' as item,
    'application/admin/controller/policy/Policyrule.php' as path,
    '✓ 已创建' as status;

SELECT 
    '菜单URL' as item,
    'policy/policyrule' as path,
    CASE 
        WHEN (SELECT COUNT(*) FROM fa_auth_rule WHERE name = 'policy/policyrule') > 0
        THEN '✓ 已配置'
        ELSE '❌ 未配置'
    END as status;

SELECT 
    'JavaScript URL' as item,
    'public/assets/js/backend/policy/policy_rule.js' as path,
    '需要手动确认配置正确' as status;

-- 9. 使用说明
SELECT '=== 使用说明 ===' as info;
SELECT '1. 确认控制器文件已重命名为 Policyrule.php' as instruction;
SELECT '2. 确认控制器类名已改为 class Policyrule' as instruction;
SELECT '3. 清除浏览器缓存并重新登录' as instruction;
SELECT '4. 访问：后台管理 → 政策问卷 → 政策规则管理' as instruction;
SELECT '5. 如果仍有问题，检查浏览器控制台错误信息' as instruction;
