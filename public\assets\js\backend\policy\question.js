define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'policy/question/index' + location.search,
                    add_url: 'policy/question/add',
                    edit_url: 'policy/question/edit',
                    del_url: 'policy/question/del',
                    multi_url: 'policy/question/multi',
                    options_url: 'policy/question/options',
                    statistics_url: 'policy/question/statistics',
                    copy_url: 'policy/question/copy',
                    table: 'policy_question',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'sort',
                sortOrder: 'asc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: 'ID', sortable: true},
                        {field: 'questionnaire.title', title: '所属问卷', operate: 'LIKE', formatter: Table.api.formatter.search},
                        {field: 'title', title: '问题标题', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'description', title: '问题描述', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {
                            field: 'type',
                            title: '问题类型',
                            searchList: {"single":"单选题","multiple":"多选题"},
                            formatter: Table.api.formatter.normal
                        },
                        {
                            field: 'is_required',
                            title: '是否必答',
                            searchList: {"0":"否","1":"是"},
                            formatter: Table.api.formatter.normal
                        },
                        {field: 'option_count', title: '选项数量', operate: false},
                        {field: 'sort', title: '排序', operate: false, sortable: true},
                        {
                            field: 'status',
                            title: '状态',
                            searchList: {"normal":"正常","hidden":"隐藏"},
                            formatter: Table.api.formatter.status
                        },
                        {field: 'createtime', title: '创建时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime, sortable: true},
                        {field: 'operate', title: '操作', table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 管理选项
            $(document).on('click', '.btn-options', function () {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Toastr.error(__('Please select at least one record'));
                    return false;
                }
                if (ids.length > 1) {
                    Toastr.error('只能选择一个问题管理选项');
                    return false;
                }
                
                Fast.api.open($.fn.bootstrapTable.defaults.extend.options_url + '?ids=' + ids.join(','), '管理答案选项', {
                    area: ['90%', '90%']
                });
            });

            // 查看统计
            $(document).on('click', '.btn-statistics', function () {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Toastr.error(__('Please select at least one record'));
                    return false;
                }
                if (ids.length > 1) {
                    Toastr.error('只能选择一个问题查看统计');
                    return false;
                }
                
                Fast.api.open($.fn.bootstrapTable.defaults.extend.statistics_url + '?ids=' + ids.join(','), '问题统计', {
                    area: ['80%', '80%']
                });
            });

            // 复制问题
            $(document).on('click', '.btn-copy', function () {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Toastr.error(__('Please select at least one record'));
                    return false;
                }
                if (ids.length > 1) {
                    Toastr.error('只能选择一个问题进行复制');
                    return false;
                }
                
                Fast.api.open($.fn.bootstrapTable.defaults.extend.copy_url + '?ids=' + ids.join(','), '复制问题', {
                    area: ['60%', '50%']
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        options: function () {
            // 选项管理页面逻辑
            Controller.api.bindevent();
            
            // 添加选项
            $(document).on('click', '.btn-add-option', function () {
                var template = $('#option-template').html();
                var index = $('.option-item').length;
                var html = template.replace(/\{index\}/g, index);
                $('#options-container').append(html);
            });

            // 删除选项
            $(document).on('click', '.btn-remove-option', function () {
                $(this).closest('.option-item').remove();
            });
        },
        statistics: function () {
            // 统计页面的初始化逻辑
            Controller.api.bindevent();
        },
        copy: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
