define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'policy/questionnaire/index' + location.search,
                    add_url: 'policy/questionnaire/add',
                    edit_url: 'policy/questionnaire/edit',
                    del_url: 'policy/questionnaire/del',
                    multi_url: 'policy/questionnaire/multi',
                    copy_url: 'policy/questionnaire/copy',
                    statistics_url: 'policy/questionnaire/statistics',
                    preview_url: 'policy/questionnaire/preview',
                    table: 'policy_questionnaire',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'sort',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'title', title: '问卷标题', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'description', title: '问卷描述', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'cover_image', title: '封面图片', operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {
                            field: 'status',
                            title: '状态',
                            searchList: {"normal":__('Normal'),"hidden":__('Hidden')},
                            formatter: Table.api.formatter.status
                        },
                        {field: 'question_count', title: '问题数量', operate: false},
                        {field: 'sort', title: '排序', operate: false, sortable: true},
                        {field: 'start_time_text', title: '开始时间', operate: 'RANGE', addclass: 'datetimerange', autocomplete: false, sortable: true},
                        {field: 'end_time_text', title: '结束时间', operate: 'RANGE', addclass: 'datetimerange', autocomplete: false, sortable: true},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime, sortable: true},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 复制问卷
            $(document).on('click', '.btn-copy', function () {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Toastr.error(__('Please select at least one record'));
                    return false;
                }
                if (ids.length > 1) {
                    Toastr.error('只能选择一个问卷进行复制');
                    return false;
                }
                
                Layer.prompt({
                    title: '复制问卷',
                    formType: 0,
                    value: '',
                    placeholder: '请输入新问卷标题'
                }, function(value, index) {
                    if (!value) {
                        Toastr.error('问卷标题不能为空');
                        return false;
                    }
                    
                    Fast.api.ajax({
                        url: $.fn.bootstrapTable.defaults.extend.copy_url,
                        data: {ids: ids.join(','), title: value}
                    }, function(data, ret) {
                        Layer.close(index);
                        Toastr.success(ret.msg);
                        table.bootstrapTable('refresh');
                    });
                });
            });

            // 查看统计
            $(document).on('click', '.btn-statistics', function () {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Toastr.error(__('Please select at least one record'));
                    return false;
                }
                if (ids.length > 1) {
                    Toastr.error('只能选择一个问卷查看统计');
                    return false;
                }
                
                Fast.api.open($.fn.bootstrapTable.defaults.extend.statistics_url + '?ids=' + ids.join(','), '问卷统计', {
                    area: ['90%', '90%']
                });
            });

            // 预览问卷
            $(document).on('click', '.btn-preview', function () {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Toastr.error(__('Please select at least one record'));
                    return false;
                }
                if (ids.length > 1) {
                    Toastr.error('只能选择一个问卷进行预览');
                    return false;
                }
                
                Fast.api.open($.fn.bootstrapTable.defaults.extend.preview_url + '?ids=' + ids.join(','), '问卷预览', {
                    area: ['80%', '90%']
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        copy: function () {
            Controller.api.bindevent();
        },
        statistics: function () {
            // 统计页面的初始化逻辑
            Controller.api.bindevent();
        },
        preview: function () {
            // 预览页面的初始化逻辑
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
