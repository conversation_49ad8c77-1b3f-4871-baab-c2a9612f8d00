-- 政策问卷管理菜单权限配置SQL

-- 1. 添加政策问卷管理主菜单
INSERT INTO `fa_auth_rule` (`name`, `title`, `type`, `status`, `condition`, `menu`, `weigh`, `icon`) VALUES
('policy', '政策问卷', 'file', 'normal', '', 'menu', 100, 'fa fa-file-text-o');

-- 获取刚插入的主菜单ID
SET @parent_id = LAST_INSERT_ID();

-- 2. 添加问卷管理子菜单
INSERT INTO `fa_auth_rule` (`pid`, `name`, `title`, `type`, `status`, `condition`, `menu`, `weigh`, `icon`) VALUES
(@parent_id, 'policy/questionnaire', '问卷管理', 'file', 'normal', '', 'menu', 90, 'fa fa-list-alt'),
(@parent_id, 'policy/question', '问题管理', 'file', 'normal', '', 'menu', 80, 'fa fa-question-circle'),
(@parent_id, 'policy/answeroption', '选项管理', 'file', 'normal', '', 'menu', 70, 'fa fa-check-square-o'),
(@parent_id, 'policy/policy', '政策管理', 'file', 'normal', '', 'menu', 60, 'fa fa-file-text-o');

-- 3. 添加问卷管理功能权限
INSERT INTO `fa_auth_rule` (`pid`, `name`, `title`, `type`, `status`, `condition`, `menu`, `weigh`) VALUES
((SELECT id FROM fa_auth_rule WHERE name = 'policy/questionnaire'), 'policy/questionnaire/index', '查看', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/questionnaire'), 'policy/questionnaire/add', '添加', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/questionnaire'), 'policy/questionnaire/edit', '编辑', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/questionnaire'), 'policy/questionnaire/del', '删除', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/questionnaire'), 'policy/questionnaire/multi', '批量操作', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/questionnaire'), 'policy/questionnaire/copy', '复制问卷', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/questionnaire'), 'policy/questionnaire/statistics', '查看统计', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/questionnaire'), 'policy/questionnaire/preview', '预览问卷', 'file', 'normal', '', '', 0);

-- 4. 添加问题管理功能权限
INSERT INTO `fa_auth_rule` (`pid`, `name`, `title`, `type`, `status`, `condition`, `menu`, `weigh`) VALUES
((SELECT id FROM fa_auth_rule WHERE name = 'policy/question'), 'policy/question/index', '查看', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/question'), 'policy/question/add', '添加', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/question'), 'policy/question/edit', '编辑', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/question'), 'policy/question/del', '删除', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/question'), 'policy/question/multi', '批量操作', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/question'), 'policy/question/options', '管理选项', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/question'), 'policy/question/statistics', '查看统计', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/question'), 'policy/question/copy', '复制问题', 'file', 'normal', '', '', 0);

-- 5. 添加选项管理功能权限
INSERT INTO `fa_auth_rule` (`pid`, `name`, `title`, `type`, `status`, `condition`, `menu`, `weigh`) VALUES
((SELECT id FROM fa_auth_rule WHERE name = 'policy/answeroption'), 'policy/answeroption/index', '查看', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/answeroption'), 'policy/answeroption/add', '添加', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/answeroption'), 'policy/answeroption/edit', '编辑', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/answeroption'), 'policy/answeroption/del', '删除', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/answeroption'), 'policy/answeroption/multi', '批量操作', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/answeroption'), 'policy/answeroption/policies', '管理政策关联', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/answeroption'), 'policy/answeroption/statistics', '查看统计', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/answeroption'), 'policy/answeroption/batchsetpolicies', '批量设置政策', 'file', 'normal', '', '', 0);

-- 6. 添加政策管理功能权限
INSERT INTO `fa_auth_rule` (`pid`, `name`, `title`, `type`, `status`, `condition`, `menu`, `weigh`) VALUES
((SELECT id FROM fa_auth_rule WHERE name = 'policy/policy'), 'policy/policy/index', '查看', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/policy'), 'policy/policy/add', '添加', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/policy'), 'policy/policy/edit', '编辑', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/policy'), 'policy/policy/del', '删除', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/policy'), 'policy/policy/multi', '批量操作', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/policy'), 'policy/policy/detail', '查看详情', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/policy'), 'policy/policy/statistics', '查看统计', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/policy'), 'policy/policy/search', '搜索政策', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/policy'), 'policy/policy/import', '导入政策', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/policy'), 'policy/policy/export', '导出政策', 'file', 'normal', '', '', 0),
((SELECT id FROM fa_auth_rule WHERE name = 'policy/policy'), 'policy/policy/copy', '复制政策', 'file', 'normal', '', '', 0);

-- 7. 为超级管理员组添加所有权限
-- 获取所有新添加的权限ID
SET @rule_ids = (
    SELECT GROUP_CONCAT(id) 
    FROM fa_auth_rule 
    WHERE name LIKE 'policy%' 
    OR name = 'policy'
);

-- 更新超级管理员组的权限
UPDATE fa_auth_group 
SET rules = CONCAT(IFNULL(rules, ''), ',', @rule_ids) 
WHERE id = 1;

-- 8. 清理权限字符串（去除开头的逗号）
UPDATE fa_auth_group 
SET rules = TRIM(LEADING ',' FROM rules) 
WHERE id = 1;

-- 9. 显示添加结果
SELECT 
    '=== 政策问卷管理菜单添加完成 ===' as info;

SELECT 
    id,
    pid,
    name,
    title,
    CASE 
        WHEN pid = 0 THEN '主菜单'
        WHEN menu = 'menu' THEN '子菜单'
        ELSE '功能权限'
    END as menu_type,
    weigh,
    status
FROM fa_auth_rule 
WHERE name LIKE 'policy%' OR name = 'policy'
ORDER BY pid, weigh DESC, id;

-- 10. 检查超级管理员权限
SELECT 
    '=== 超级管理员权限检查 ===' as info;

SELECT 
    g.id,
    g.name,
    CASE 
        WHEN g.rules LIKE '%policy%' THEN '已包含政策问卷管理权限'
        ELSE '未包含政策问卷管理权限'
    END as permission_status
FROM fa_auth_group g 
WHERE g.id = 1;
