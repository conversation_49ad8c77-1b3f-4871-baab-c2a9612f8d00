<div class="panel panel-default panel-intro">
    
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="platform">
            <li class="{:$Think.get.platform === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            <li class="{:$Think.get.platform === 'quota' ? 'active' : ''}"><a href="#t-quota" data-value="quota" data-toggle="tab">额度开通</a></li>
            <li class="{:$Think.get.platform === 'admin' ? 'active' : ''}"><a href="#t-admin" data-value="admin" data-toggle="tab">管理员开通</a></li>
            <li class="{:$Think.get.platform === 'self' ? 'active' : ''}"><a href="#t-self" data-value="wxmin" data-toggle="tab">自主购买</a></li>
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('ylgw_order/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
