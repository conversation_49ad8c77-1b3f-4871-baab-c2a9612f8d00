# 政策问卷系统API使用说明

## 概述

政策问卷系统提供了完整的API接口，支持政策管理和问卷管理功能。系统支持登录用户和匿名用户使用。

## 基础信息

- **基础URL**: `http://your-domain.com`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Token认证（可选）

## 通用响应格式

所有API接口都遵循统一的响应格式：

```json
{
    "code": 1,              // 状态码：1-成功，0-失败
    "msg": "操作成功",       // 响应消息
    "time": "1640995200",   // 服务器时间戳
    "data": {}              // 响应数据
}
```

## 政策管理API

### 1. 获取政策列表

**接口地址**: `GET /api/policy.policy/lists`

**请求参数**:
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认10
- `category` (可选): 政策分类
- `keyword` (可选): 搜索关键词

**响应示例**:
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "title": "高龄津贴政策",
                "summary": "为80岁以上老人提供生活津贴",
                "category": "elderly_care",
                "cover_image": "/uploads/policy/cover1.jpg",
                "view_count": 1250,
                "publish_date": "2024-01-01"
            }
        ],
        "total": 50,
        "page": 1,
        "limit": 10
    }
}
```

### 2. 获取政策详情

**接口地址**: `GET /api/policy.policy/detail`

**请求参数**:
- `id` (必填): 政策ID

**响应示例**:
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "id": 1,
        "title": "高龄津贴政策",
        "content": "详细的政策内容...",
        "summary": "为80岁以上老人提供生活津贴",
        "category": "elderly_care",
        "tags": ["养老", "津贴", "高龄"],
        "cover_image": "/uploads/policy/cover1.jpg",
        "source": "民政部",
        "publish_date": "2024-01-01",
        "view_count": 1251
    }
}
```

### 3. 获取推荐政策

**接口地址**: `POST /api/policy.policy/recommend`

**请求参数**:
```json
{
    "answer_option_ids": [1, 2, 3],  // 答案选项ID数组
    "limit": 10                      // 返回数量，默认10
}
```

**响应示例**:
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": [
        {
            "id": 1,
            "title": "高龄津贴政策",
            "summary": "为80岁以上老人提供生活津贴",
            "category": "elderly_care",
            "cover_image": "/uploads/policy/cover1.jpg",
            "match_score": 85.5
        }
    ]
}
```

## 问卷管理API

### 1. 获取问卷列表

**接口地址**: `GET /api/policy.questionnaire/lists`

**请求参数**:
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认10
- `keyword` (可选): 搜索关键词
- `status` (可选): 状态筛选，默认normal

**响应示例**:
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "title": "养老政策适配问卷",
                "description": "帮助您找到最适合的养老政策",
                "cover_image": "/uploads/questionnaire/cover1.jpg",
                "estimated_time": 300,
                "total_questions": 10,
                "start_time": 1640995200,
                "end_time": 1672531200
            }
        ],
        "total": 5,
        "page": 1,
        "limit": 10
    }
}
```

### 2. 获取问卷详情

**接口地址**: `GET /api/policy.questionnaire/detail`

**请求参数**:
- `id` (必填): 问卷ID

**响应示例**:
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "id": 1,
        "title": "养老政策适配问卷",
        "description": "帮助您找到最适合的养老政策",
        "estimated_time": 300,
        "total_questions": 3,
        "questions": [
            {
                "id": 1,
                "title": "您的年龄段是？",
                "description": "请选择您的年龄段",
                "type": "single",
                "is_required": 1,
                "sort": 1,
                "options": [
                    {
                        "id": 1,
                        "option_text": "60-70岁",
                        "sort": 1
                    },
                    {
                        "id": 2,
                        "option_text": "70-80岁",
                        "sort": 2
                    }
                ]
            }
        ]
    }
}
```

### 3. 提交问卷答案

**接口地址**: `POST /api/policy.questionnaire/submit`

**请求头**:
- `token` (可选): 用户登录token

**请求参数**:
```json
{
    "questionnaire_id": 1,
    "answers": [
        {
            "question_id": 1,
            "option_ids": [1]
        },
        {
            "question_id": 2,
            "option_ids": [3]
        },
        {
            "question_id": 3,
            "option_ids": [1, 2]
        }
    ],
    "start_time": 1640995200,
    "session_id": "guest_63f8b2c1a5d4e"
}
```

**响应示例**:
```json
{
    "code": 1,
    "msg": "提交成功",
    "data": {
        "session_id": "guest_63f8b2c1a5d4e",
        "questionnaire_id": 1,
        "completion_time": 180,
        "total_score": 85.5,
        "matched_rules_count": 3,
        "recommended_policies_count": 5
    }
}
```

### 4. 获取问卷结果

**接口地址**: `GET /api/policy.questionnaire/getResult`

**请求头**:
- `token` (可选): 用户登录token

**请求参数**:
- `questionnaire_id` (必填): 问卷ID
- `session_id` (可选): 会话ID，匿名用户必填

**响应示例**:
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "questionnaire_id": 1,
        "completion_time": 180,
        "total_score": 85.5,
        "matched_rules": [
            {
                "rule_id": 1,
                "rule_name": "高龄津贴规则",
                "match_score": 95.0,
                "policy_ids": [1, 2]
            }
        ],
        "recommended_policies": [
            {
                "id": 1,
                "title": "高龄津贴政策",
                "summary": "为80岁以上老人提供生活津贴",
                "category": "elderly_care",
                "cover_image": "/uploads/policy/cover1.jpg",
                "view_count": 1250,
                "publish_date": "2024-01-01"
            }
        ],
        "submit_time": 1640995200
    }
}
```

## 错误处理

当API调用失败时，会返回错误信息：

```json
{
    "code": 0,
    "msg": "错误信息",
    "time": "1640995200",
    "data": null
}
```

常见错误码：
- `0`: 操作失败
- `1`: 操作成功

## 使用流程

### 匿名用户答题流程

1. 获取问卷列表 → 选择问卷
2. 获取问卷详情 → 展示问题和选项
3. 提交答案 → 获取session_id
4. 获取结果 → 使用session_id查询推荐政策

### 登录用户答题流程

1. 用户登录获取token
2. 获取问卷列表 → 选择问卷
3. 获取问卷详情 → 展示问题和选项
4. 提交答案（携带token）
5. 获取结果（携带token）

## 注意事项

1. 所有时间戳均为Unix时间戳（秒）
2. 图片路径为相对路径，需要拼接完整URL
3. 匿名用户需要保存session_id用于查询结果
4. 问卷有有效期限制，过期问卷无法答题
5. 答案提交后会覆盖之前的答题记录
