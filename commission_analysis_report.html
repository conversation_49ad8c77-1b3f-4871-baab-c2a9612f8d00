<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目分佣逻辑分析报告</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.8;
            color: #333;
            background-color: #fdfdfd;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        h1 {
            text-align: center;
            border-bottom: none;
            font-size: 2.5em;
        }
        code, pre {
            background-color: #ecf0f1;
            padding: 3px 6px;
            border-radius: 4px;
            font-family: "Courier New", Courier, monospace;
            color: #c0392b;
            font-size: 0.95em;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 10px;
        }
        .module {
            border-left: 5px solid #3498db;
            padding-left: 25px;
            margin-top: 20px;
            margin-bottom: 40px;
            background-color: #f9f9f9;
            padding-top: 15px;
            padding-bottom: 15px;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            font-size: 1em;
            min-width: 400px;
            border-radius: 5px 5px 0 0;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
        }
        table thead tr {
            background-color: #3498db;
            color: #ffffff;
            text-align: left;
            font-weight: bold;
        }
        table th, table td {
            padding: 12px 15px;
            border: 1px solid #dddddd;
        }
        table tbody tr {
            border-bottom: 1px solid #dddddd;
        }
        table tbody tr:nth-of-type(even) {
            background-color: #f3f3f3;
        }
        table tbody tr:last-of-type {
            border-bottom: 2px solid #3498db;
        }
</style>
</head>
<body>
    <div class="container">
        <h1>项目分佣逻辑分析报告</h1>

        <h2>核心对比总结</h2>
        <table>
            <thead>
                <tr>
                    <th>特性</th>
                    <th>商品购买 (<code>wanlshop</code>)</th>
                    <th>课程购买 (<code>xiluedu</code>)</th>
                    <th>服务购买 (<code>service</code>)</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>分佣基础</strong></td>
                    <td>订单利润 (商品支付价 - 商品成本价)</td>
                    <td>多种计算模式（标准、新、价格差）</td>
                    <td>订单最终结算价 (<code>settle_price</code>)</td>
                </tr>
                <tr>
                    <td><strong>触发时机</strong></td>
                    <td>用户<strong>确认收货</strong>时</td>
                    <td>用户<strong>支付成功</strong>后</td>
                    <td>用户<strong>确认服务完成</strong>时</td>
                </tr>
                <tr>
                    <td><strong>核心角色</strong></td>
                    <td>供应商, 养老院长, 城市运营商, 养老顾问</td>
                    <td><strong>老师</strong>, 养老院长, 城市运营商, 养老顾问</td>
                    <td><strong>服务者</strong>, 城市运营商, 养老院长, 养老顾问</td>
                </tr>
                <tr>
                    <td><strong>佣金计算</strong></td>
                    <td>下单时预计算，确认收货后执行</td>
                    <td>支付成功后立即计算并分配</td>
                    <td>确认服务后一次性计算并分配</td>
                </tr>
                <tr>
                    <td><strong>关键文件/方法</strong></td>
                    <td><code>Order.php</code> -> <code>confirmOrder()</code></td>
                    <td><code>CourseOrder::pay_notify()</code></td>
                    <td><code>Rebate.php</code> -> <code>orderRebate()</code></td>
                </tr>
            </tbody>
        </table>

        <h2>一、 整体分佣体系概述</h2>
        <p>系统采用了一套复杂且灵活的多层级分佣体系，其核心是基于不同角色的贡献进行利润分配。不同业务模块（商品、课程、服务）的分佣逻辑既有共通之处，也存在显著差异，以适应各自的业务特性。</p>
        <h3>核心分佣角色包括：</h3>
        <ul>
            <li>平台 (Platform)</li>
            <li>城市运营商 (is_qydl)</li>
            <li>养老院长 (is_sqdl)</li>
            <li>养老顾问 (is_ylgw)</li>
            <li>供应商 (Shop Owner)</li>
            <li>老师 (Teacher)</li>
            <li>服务者 (Service Provider)</li>
        </ul>

        <h2>二、 各模块分佣逻辑详解</h2>

        <div class="module">
            <h3>1. 商品购买 (<code>wanlshop</code> 模块)</h3>
            <ul>
                <li><strong>分佣基础</strong>: <strong>订单利润</strong> (商品支付价 - 商品成本价)。</li>
                <li><strong>触发时机</strong>: 用户<strong>确认收货</strong>时执行分佣，但佣金在<strong>下单时</strong>就已预先计算并存储在订单商品表中。</li>
                <li><strong>核心逻辑</strong>:
                    <ul>
                        <li>供应商获得商品的成本价。</li>
                        <li>养老院长和城市运营商根据商品独立或全局配置的比例，从利润中获得分成。</li>
                        <li><strong>养老顾问</strong>的佣金是其上级<strong>养老院长佣金的50%</strong>，并存在特殊的“待结算”逻辑（佣金发放到上级账户，但计入顾问的 <code>ylgw_total_commission</code> 字段）。</li>
                        <li>平台获得所有角色分配后的剩余利润。</li>
                    </ul>
                </li>
                <li><strong>关键文件与方法</strong>:
                    <ul>
                        <li><code>application/api/controller/wanlshop/Order.php</code>: <code>calculateCommission()</code>, <code>confirmOrder()</code></li>
                        <li><code>application/common/model/User.php</code>: <code>getOneSq()</code>, <code>getOneQy()</code>, <code>getOneYlgw()</code>, <code>money()</code>, <code>xianxiamoney()</code></li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="module">
            <h3>2. 课程购买 (<code>xiluedu</code> 模块)</h3>
            <ul>
                <li><strong>分佣基础</strong>: <strong>最为复杂</strong>，存在多种计算模式（标准模式、新模式、价格差模式）。</li>
                <li><strong>触发时机</strong>: 用户<strong>支付成功</strong>后立即触发。</li>
                <li><strong>核心逻辑</strong>:
                    <ul>
                        <li>引入了 <strong>老师</strong> 角色，其佣金最先被计算和分配。</li>
                        <li>分佣顺序严格：老师 -> 养老院长 -> 城市运营商 -> 养老顾问。</li>
                        <li>同样存在养老顾问的“待结算”佣金逻辑。</li>
                    </ul>
                </li>
                <li><strong>关键文件与方法</strong>:
                    <ul>
                        <li><code>app\common\model\xiluedu\CourseOrder::pay_notify()</code></li>
                        <li><code>app\common\model\xiluedu\OfflineOrder::pay_notify()</code></li>
                        <li><code>Course16.php</code> (针对特殊课程)</li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="module">
            <h3>3. 服务购买 (<code>service</code> 模块)</h3>
            <ul>
                <li><strong>分佣基础</strong>: <strong>订单最终结算价 (<code>settle_price</code>)</strong>。</li>
                <li><strong>触发时机</strong>: 用户<strong>确认服务完成</strong>时触发。</li>
                <li><strong>核心逻辑</strong>:
                    <ul>
                        <li>分佣比例由 <strong>订单所在城市</strong> 在 <code>service_city_config</code> 表中预设。</li>
                        <li>在用户确认服务后，系统会一次性计算所有角色的佣金，并立即发放到各自的账户余额中。</li>
                    </ul>
                </li>
                <li><strong>关键文件与方法</strong>:
                    <ul>
                        <li><code>application/api/controller/service/Order.php</code>: <code>createOrder()</code>, <code>pay()</code>, <code>userConfirm()</code></li>
                        <li><code>application/api/model/service/Order.php</code>: <code>userConfirm()</code></li>
                        <li><code>application/api/model/service/Rebate.php</code>: <code>orderRebate()</code></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</body>
</html>