-- 检查政策规则管理菜单是否正确添加

-- 1. 检查政策问卷主菜单
SELECT '=== 检查政策问卷主菜单 ===' as info;

SELECT 
    id,
    pid,
    name,
    title,
    icon,
    ismenu,
    weigh,
    status,
    CASE 
        WHEN name = 'policy' THEN '✓ 主菜单存在'
        ELSE '子菜单'
    END as check_result
FROM fa_auth_rule 
WHERE name = 'policy'
ORDER BY id;

-- 2. 检查政策规则管理子菜单
SELECT '=== 检查政策规则管理子菜单 ===' as info;

SELECT 
    id,
    pid,
    name,
    title,
    icon,
    ismenu,
    weigh,
    status,
    CASE 
        WHEN name = 'policy/policyrule' THEN '✓ 政策规则管理菜单存在'
        ELSE '其他菜单'
    END as check_result
FROM fa_auth_rule 
WHERE name = 'policy/policyrule'
ORDER BY id;

-- 3. 检查所有政策规则管理功能权限
SELECT '=== 检查政策规则管理功能权限 ===' as info;

SELECT 
    id,
    pid,
    name,
    title,
    ismenu,
    status,
    CASE 
        WHEN name LIKE 'policy/policyrule/%' THEN '✓ 功能权限'
        ELSE '其他'
    END as check_result
FROM fa_auth_rule 
WHERE name LIKE 'policy/policyrule/%'
ORDER BY name;

-- 4. 检查完整的政策问卷菜单结构
SELECT '=== 完整的政策问卷菜单结构 ===' as info;

SELECT 
    r.id,
    r.pid,
    r.name,
    r.title,
    r.icon,
    r.ismenu,
    r.weigh,
    r.status,
    CASE 
        WHEN r.pid = 0 THEN '📁 主菜单'
        WHEN r.ismenu = 1 THEN '📂 子菜单'
        ELSE '⚙️ 功能权限'
    END as menu_type
FROM fa_auth_rule r 
WHERE r.name = 'policy' 
   OR r.name LIKE 'policy/%'
ORDER BY 
    CASE WHEN r.pid = 0 THEN 0 ELSE r.pid END,
    r.weigh DESC, 
    r.id;

-- 5. 检查超级管理员权限
SELECT '=== 检查超级管理员权限 ===' as info;

SELECT 
    g.id,
    g.name,
    CASE 
        WHEN g.rules LIKE '%policy/policyrule%' THEN '✓ 已包含政策规则管理权限'
        ELSE '❌ 未包含政策规则管理权限'
    END as permission_status,
    CHAR_LENGTH(g.rules) as rules_count
FROM fa_auth_group g 
WHERE g.id = 1;

-- 6. 统计检查结果
SELECT '=== 检查结果统计 ===' as info;

SELECT 
    '政策问卷主菜单' as item,
    CASE 
        WHEN (SELECT COUNT(*) FROM fa_auth_rule WHERE name = 'policy') > 0 
        THEN '✓ 存在' 
        ELSE '❌ 不存在' 
    END as status;

SELECT 
    '政策规则管理子菜单' as item,
    CASE 
        WHEN (SELECT COUNT(*) FROM fa_auth_rule WHERE name = 'policy/policyrule') > 0 
        THEN '✓ 存在' 
        ELSE '❌ 不存在' 
    END as status;

SELECT 
    '政策规则管理功能权限' as item,
    CONCAT('共 ', COUNT(*), ' 个权限') as status
FROM fa_auth_rule 
WHERE name LIKE 'policy/policyrule/%';

SELECT 
    '超级管理员权限' as item,
    CASE 
        WHEN (SELECT rules FROM fa_auth_group WHERE id = 1) LIKE '%policy/policyrule%' 
        THEN '✓ 已配置' 
        ELSE '❌ 未配置' 
    END as status;

-- 7. 问题诊断
SELECT '=== 问题诊断 ===' as info;

-- 检查是否有重复菜单
SELECT 
    '重复菜单检查' as check_type,
    name,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) > 1 THEN '❌ 有重复'
        ELSE '✓ 正常'
    END as result
FROM fa_auth_rule 
WHERE name LIKE 'policy%'
GROUP BY name
HAVING COUNT(*) > 1;

-- 检查菜单层级关系
SELECT 
    '菜单层级检查' as check_type,
    r1.name as menu_name,
    r1.title as menu_title,
    r2.name as parent_name,
    r2.title as parent_title,
    CASE 
        WHEN r1.pid > 0 AND r2.id IS NULL THEN '❌ 父菜单不存在'
        WHEN r1.pid = 0 THEN '✓ 主菜单'
        ELSE '✓ 层级正常'
    END as result
FROM fa_auth_rule r1
LEFT JOIN fa_auth_rule r2 ON r1.pid = r2.id
WHERE r1.name LIKE 'policy%'
ORDER BY r1.pid, r1.weigh DESC;

-- 8. 修复建议
SELECT '=== 修复建议 ===' as info;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM fa_auth_rule WHERE name = 'policy') = 0 
        THEN '1. 需要添加政策问卷主菜单'
        WHEN (SELECT COUNT(*) FROM fa_auth_rule WHERE name = 'policy/policyrule') = 0 
        THEN '2. 需要添加政策规则管理子菜单'
        WHEN (SELECT COUNT(*) FROM fa_auth_rule WHERE name LIKE 'policy/policyrule/%') < 7 
        THEN '3. 需要添加完整的功能权限'
        WHEN (SELECT rules FROM fa_auth_group WHERE id = 1) NOT LIKE '%policy/policyrule%' 
        THEN '4. 需要为超级管理员分配权限'
        ELSE '✓ 菜单配置完整，如果看不到菜单请清除浏览器缓存并重新登录'
    END as suggestion;
