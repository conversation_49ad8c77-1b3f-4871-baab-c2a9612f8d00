<?php

namespace addons\betterform;

use app\common\library\Menu;
use think\Addons;
use think\Loader;

/**
 * 插件
 */
class Betterform extends Addons
{

    /**
     * 插件安装方法
     * @return bool
     */
    public function install()
    {

        return true;
    }

    /**
     * 插件卸载方法
     * @return bool
     */
    public function uninstall()
    {

        return true;
    }

    /**
     * 插件启用方法
     * @return bool
     */
    public function enable()
    {

        return true;
    }

    /**
     * 插件禁用方法
     * @return bool
     */
    public function disable()
    {

        return true;
    }

    public function viewFilter(&$content)
    {
        $request = \think\Request::instance();
        $dispatch = $request->dispatch();
        if (!$dispatch) {
            return;
        }

        if (!$request->module() || $request->module() !== 'admin') {
            return;
        }

        $config = get_addon_config('betterform');

        //在head前引入CSS
        $content = preg_replace("/<\/head>/i", "<link href='/assets/addons/betterform/css/common.css' rel='stylesheet' />" . "\n\$0", $content);

        //如果不存在表单
        if (!preg_match('/<form (.*?)data-toggle="validator"/i', $content)) {
            return;
        }

        $content = preg_replace_callback('/(?=<div class="form\-group">)([\s\S]*?)data\-rule="(.*?)required(.*?)"/im', function ($matches) use ($config) {
            return str_replace("form-group", "form-group required-{$config['asteriskposition']}", $matches[0]);
        }, $content);
    }

    /**
     * @param $params
     */
    public function configInit(&$params)
    {
        $config = $this->getConfig();

        $config['area'] = preg_match("/\[(.*?)\]/i", $config['area']) ? array_slice(array_values((array)json_decode($config['area'], true)), 0, 2) : $config['area'];
        $config['shade'] = floatval($config['shade']);
        $config['shadeClose'] = boolval($config['shadeClose']);
        $params['betterform'] = $config;
    }

}
