{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "政策问卷系统API", "description": "政策问卷系统的完整API接口文档，包含政策管理和问卷管理功能", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "政策问卷系统API", "id": 1, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "政策问卷系统的完整API接口", "items": [{"name": "政策管理", "id": 10, "parentId": 1, "description": "政策相关的API接口", "items": [{"name": "获取政策列表", "api": {"id": "101", "method": "get", "path": "/api/policy.policy/lists", "parameters": {"query": [{"name": "page", "required": false, "description": "页码，默认1", "example": "1", "type": "integer", "schema": {"type": "integer", "default": 1, "minimum": 1}}, {"name": "limit", "required": false, "description": "每页数量，默认10", "example": "10", "type": "integer", "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}}, {"name": "category", "required": false, "description": "政策分类", "example": "elderly_care", "type": "string", "schema": {"type": "string"}}, {"name": "keyword", "required": false, "description": "搜索关键词", "example": "养老", "type": "string", "schema": {"type": "string"}}], "header": [], "path": [], "cookie": []}, "responses": [{"id": "101_success", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "example": 1}, "msg": {"type": "string", "example": "获取成功"}, "time": {"type": "string", "example": "1640995200"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "高龄津贴政策"}, "summary": {"type": "string", "example": "为80岁以上老人提供生活津贴"}, "category": {"type": "string", "example": "elderly_care"}, "cover_image": {"type": "string", "example": "/uploads/policy/cover1.jpg"}, "view_count": {"type": "integer", "example": 1250}, "publish_date": {"type": "string", "example": "2024-01-01"}, "createtime": {"type": "integer", "example": 1640995200}}}}, "total": {"type": "integer", "example": 50}, "page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}, "description": "成功返回政策列表", "contentType": "json"}], "description": "获取政策列表，支持分页、分类筛选和关键词搜索", "tags": ["政策管理"], "status": "released"}}, {"name": "获取政策详情", "api": {"id": "102", "method": "get", "path": "/api/policy.policy/detail", "parameters": {"query": [{"name": "id", "required": true, "description": "政策ID", "example": "1", "type": "integer", "schema": {"type": "integer", "minimum": 1}}], "header": [], "path": [], "cookie": []}, "responses": [{"id": "102_success", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "example": 1}, "msg": {"type": "string", "example": "获取成功"}, "time": {"type": "string", "example": "1640995200"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "高龄津贴政策"}, "content": {"type": "string", "example": "详细的政策内容..."}, "summary": {"type": "string", "example": "为80岁以上老人提供生活津贴"}, "category": {"type": "string", "example": "elderly_care"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["养老", "津贴", "高龄"]}, "cover_image": {"type": "string", "example": "/uploads/policy/cover1.jpg"}, "attachment": {"type": "string", "example": "/uploads/policy/doc1.pdf"}, "source": {"type": "string", "example": "民政部"}, "publish_date": {"type": "string", "example": "2024-01-01"}, "effective_date": {"type": "string", "example": "2024-01-01"}, "view_count": {"type": "integer", "example": 1251}, "createtime": {"type": "integer", "example": 1640995200}}}}}, "description": "成功返回政策详情", "contentType": "json"}], "description": "获取政策详细信息，会自动增加查看次数", "tags": ["政策管理"], "status": "released"}}, {"name": "获取推荐政策", "api": {"id": "103", "method": "post", "path": "/api/policy.policy/recommend", "parameters": {"query": [], "header": [], "path": [], "cookie": []}, "requestBody": {"type": "application/json", "parameters": [{"name": "answer_option_ids", "required": true, "description": "答案选项ID数组", "type": "array", "schema": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}}, {"name": "limit", "required": false, "description": "返回数量，默认10", "type": "integer", "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 50}}]}, "responses": [{"id": "103_success", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "example": 1}, "msg": {"type": "string", "example": "获取成功"}, "time": {"type": "string", "example": "1640995200"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "高龄津贴政策"}, "summary": {"type": "string", "example": "为80岁以上老人提供生活津贴"}, "category": {"type": "string", "example": "elderly_care"}, "cover_image": {"type": "string", "example": "/uploads/policy/cover1.jpg"}, "match_score": {"type": "number", "example": 85.5, "description": "匹配度分数"}}}}}}, "description": "成功返回推荐政策列表", "contentType": "json"}], "description": "根据用户答题选项推荐相关政策", "tags": ["政策管理"], "status": "released"}}]}, {"name": "问卷管理", "id": 20, "parentId": 1, "description": "问卷相关的API接口", "items": [{"name": "获取问卷列表", "api": {"id": "201", "method": "get", "path": "/api/policy.questionnaire/lists", "parameters": {"query": [{"name": "page", "required": false, "description": "页码，默认1", "example": "1", "type": "integer", "schema": {"type": "integer", "default": 1, "minimum": 1}}, {"name": "limit", "required": false, "description": "每页数量，默认10", "example": "10", "type": "integer", "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}}, {"name": "keyword", "required": false, "description": "搜索关键词", "example": "养老政策", "type": "string", "schema": {"type": "string"}}, {"name": "status", "required": false, "description": "状态筛选：normal-正常，hidden-隐藏", "example": "normal", "type": "string", "schema": {"type": "string", "enum": ["normal", "hidden"], "default": "normal"}}], "header": [], "path": [], "cookie": []}, "responses": [{"id": "201_success", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "example": 1}, "msg": {"type": "string", "example": "获取成功"}, "time": {"type": "string", "example": "1640995200"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "养老政策适配问卷"}, "description": {"type": "string", "example": "帮助您找到最适合的养老政策"}, "cover_image": {"type": "string", "example": "/uploads/questionnaire/cover1.jpg"}, "estimated_time": {"type": "integer", "example": 300, "description": "预计完成时间（秒）"}, "total_questions": {"type": "integer", "example": 10}, "start_time": {"type": "integer", "example": 1640995200}, "end_time": {"type": "integer", "example": 1672531200}, "createtime": {"type": "integer", "example": 1640995200}}}}, "total": {"type": "integer", "example": 5}, "page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}}}, "description": "成功返回问卷列表", "contentType": "json"}], "description": "获取所有可用的政策问卷列表，只显示在有效期内的问卷", "tags": ["问卷管理"], "status": "released"}}, {"name": "获取问卷详情", "api": {"id": "202", "method": "get", "path": "/api/policy.questionnaire/detail", "parameters": {"query": [{"name": "id", "required": true, "description": "问卷ID", "example": "1", "type": "integer", "schema": {"type": "integer", "minimum": 1}}], "header": [], "path": [], "cookie": []}, "responses": [{"id": "202_success", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "example": 1}, "msg": {"type": "string", "example": "获取成功"}, "time": {"type": "string", "example": "1640995200"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "养老政策适配问卷"}, "description": {"type": "string", "example": "帮助您找到最适合的养老政策"}, "cover_image": {"type": "string", "example": "/uploads/questionnaire/cover1.jpg"}, "estimated_time": {"type": "integer", "example": 300}, "total_questions": {"type": "integer", "example": 3}, "start_time": {"type": "integer", "example": 1640995200}, "end_time": {"type": "integer", "example": 1672531200}, "questions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "您的年龄段是？"}, "description": {"type": "string", "example": "请选择您的年龄段"}, "type": {"type": "string", "example": "single", "enum": ["single", "multiple"]}, "is_required": {"type": "integer", "example": 1, "description": "是否必答：1-是，0-否"}, "sort": {"type": "integer", "example": 1}, "options": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "option_text": {"type": "string", "example": "60-70岁"}, "sort": {"type": "integer", "example": 1}}}}}}}}}}}, "description": "成功返回问卷详情，包含所有问题和选项", "contentType": "json"}], "description": "获取问卷详细信息，包含所有问题和选项，会检查问卷有效期", "tags": ["问卷管理"], "status": "released"}}, {"name": "提交问卷答案", "api": {"id": "203", "method": "post", "path": "/api/policy.questionnaire/submit", "parameters": {"query": [], "header": [{"name": "token", "required": false, "description": "用户登录token（可选，未登录用户可匿名答题）", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...", "type": "string", "schema": {"type": "string"}}], "path": [], "cookie": []}, "requestBody": {"type": "application/json", "parameters": [{"name": "questionnaire_id", "required": true, "description": "问卷ID", "type": "integer", "schema": {"type": "integer", "minimum": 1, "example": 1}}, {"name": "answers", "required": true, "description": "答案数组，每个元素包含question_id和option_ids", "type": "array", "schema": {"type": "array", "items": {"type": "object", "properties": {"question_id": {"type": "integer", "example": 1}, "option_ids": {"type": "array", "items": {"type": "integer"}, "example": [1]}}}, "example": [{"question_id": 1, "option_ids": [1]}, {"question_id": 2, "option_ids": [3]}, {"question_id": 3, "option_ids": [1, 2]}]}}, {"name": "start_time", "required": false, "description": "开始答题时间戳", "type": "integer", "schema": {"type": "integer", "example": 1640995200}}, {"name": "session_id", "required": false, "description": "会话ID，用于匿名用户标识", "type": "string", "schema": {"type": "string", "example": "guest_63f8b2c1a5d4e"}}]}, "responses": [{"id": "203_success", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "example": 1}, "msg": {"type": "string", "example": "提交成功"}, "time": {"type": "string", "example": "1640995200"}, "data": {"type": "object", "properties": {"session_id": {"type": "string", "example": "guest_63f8b2c1a5d4e", "description": "会话ID，用于后续查询结果"}, "questionnaire_id": {"type": "integer", "example": 1}, "completion_time": {"type": "integer", "example": 180, "description": "完成时间（秒）"}, "total_score": {"type": "number", "example": 85.5, "description": "总分"}, "matched_rules_count": {"type": "integer", "example": 3, "description": "匹配的规则数量"}, "recommended_policies_count": {"type": "integer", "example": 5, "description": "推荐的政策数量"}}}}}, "description": "成功提交答案并返回基本结果信息", "contentType": "json"}], "description": "提交用户的问卷答案，系统会根据答案匹配相应的政策规则并推荐政策", "tags": ["问卷管理"], "status": "released"}}, {"name": "获取问卷结果", "api": {"id": "204", "method": "get", "path": "/api/policy.questionnaire/getResult", "parameters": {"query": [{"name": "questionnaire_id", "required": true, "description": "问卷ID", "example": "1", "type": "integer", "schema": {"type": "integer", "minimum": 1}}, {"name": "session_id", "required": false, "description": "会话ID，用于匿名用户查询结果", "example": "guest_63f8b2c1a5d4e", "type": "string", "schema": {"type": "string"}}], "header": [{"name": "token", "required": false, "description": "用户登录token（可选）", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...", "type": "string", "schema": {"type": "string"}}], "path": [], "cookie": []}, "responses": [{"id": "204_success", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "example": 1}, "msg": {"type": "string", "example": "获取成功"}, "time": {"type": "string", "example": "1640995200"}, "data": {"type": "object", "properties": {"questionnaire_id": {"type": "integer", "example": 1}, "completion_time": {"type": "integer", "example": 180, "description": "完成时间（秒）"}, "total_score": {"type": "number", "example": 85.5, "description": "总分"}, "matched_rules": {"type": "array", "items": {"type": "object", "properties": {"rule_id": {"type": "integer", "example": 1}, "rule_name": {"type": "string", "example": "高龄津贴规则"}, "match_score": {"type": "number", "example": 95.0}, "policy_ids": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}}}, "description": "匹配的规则列表"}, "recommended_policies": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "高龄津贴政策"}, "summary": {"type": "string", "example": "为80岁以上老人提供生活津贴"}, "category": {"type": "string", "example": "elderly_care"}, "cover_image": {"type": "string", "example": "/uploads/policy/cover1.jpg"}, "view_count": {"type": "integer", "example": 1250}, "publish_date": {"type": "string", "example": "2024-01-01"}}}, "description": "推荐的政策列表"}, "submit_time": {"type": "integer", "example": 1640995200, "description": "提交时间戳"}}}}}, "description": "成功返回问卷结果和推荐政策", "contentType": "json"}], "description": "获取用户的问卷结果和推荐政策，支持登录用户和匿名用户", "tags": ["问卷管理"], "status": "released"}}]}]}]}