<?php

namespace app\admin\controller\policy;

use app\common\controller\Backend;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 政策规则管理
 */
class Policyrule extends Backend
{
    /**
     * PolicyRule模型对象
     * @var \app\admin\model\policy\PolicyRule
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\policy\PolicyRule;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 获取查询参数
            $questionnaireId = $this->request->param('questionnaire_id', 0);
            if ($questionnaireId) {
                $where[] = ['questionnaire_id', '=', $questionnaireId];
            }

            $list = $this->model
                ->with(['questionnaire'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            // 调试信息
            // \think\Log::write('查询到的规则数量: ' . count($list), 'debug');

            foreach ($list as $row) {

                // 处理关联数据 - 确保问卷数据正确传递
                if ($row->questionnaire) {
                    $row->getRelation('questionnaire')->visible(['id', 'title']);
                    // 直接设置questionnaire_title字段
                    $row->questionnaire_title = $row->questionnaire->title;
                } else {
                    $row->questionnaire_title = '未关联问卷';
                }

                // 调试信息
                // \think\Log::write('PolicyRule ID: ' . $row->id . ', questionnaire_id: ' . $row->questionnaire_id . ', questionnaire_title: ' . $row->questionnaire_title, 'debug');

                // 处理政策ID列表
                $policyIds = json_decode($row->policy_ids, true);
                if (is_array($policyIds) && !empty($policyIds)) {
                    $policies = \app\admin\model\policy\Policy::where('id', 'in', $policyIds)->select();
                    $policyNames = [];
                    foreach ($policies as $policy) {
                        $policyNames[] = $policy->title;
                    }
                    $row->policy_names = implode(', ', $policyNames);
                } else {
                    $row->policy_names = '无关联政策';
                }

                // 获取条件数量
                $conditionCount = \app\admin\model\policy\PolicyRuleCondition::where('rule_id', $row->id)->count();
                $row->condition_count = $conditionCount;

                // 获取条件描述
                $conditionModel = new \app\admin\model\policy\PolicyRuleCondition;
                $conditionDescription = $conditionModel->getConditionDescription($row->id);
                $row->condition_description = $conditionDescription ?: '无条件';

                // 生成条件简要描述
                if ($conditionDescription && $conditionDescription !== '无条件') {
                    $row->condition_summary = mb_strlen($conditionDescription) > 20 ?
                        mb_substr($conditionDescription, 0, 20) . '...' : $conditionDescription;
                } else {
                    $row->condition_summary = '无条件';
                }

                // 设置可见字段
                $row->visible(['id', 'questionnaire_id', 'name', 'description', 'policy_ids', 'weight', 'status', 'sort', 'createtime', 'status_text', 'condition_count', 'policy_names', 'condition_description', 'questionnaire_title', 'condition_summary']);

                // 处理关联数据的可见性
                if ($row->questionnaire) {
                    $row->getRelation('questionnaire')->visible(['id', 'title']);
                }
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                
                // 处理政策ID列表
                if (isset($params['policy_ids']) && is_array($params['policy_ids'])) {
                    $params['policy_ids'] = json_encode($params['policy_ids']);
                } else {
                    $params['policy_ids'] = '[]';
                }
                
                // 处理条件
                $conditions = $this->request->post("conditions/a");
                if (empty($conditions)) {
                    $this->error(__('至少需要一个条件'));
                }
                
                // 验证条件
                $conditionModel = new \app\admin\model\policy\PolicyRuleCondition;
                $validateResult = $conditionModel->validateConditions($conditions);
                if (!$validateResult['valid']) {
                    $this->error($validateResult['message']);
                }
                
                $result = false;
                Db::startTrans();
                try {
                    // 插入规则
                    $result = $this->model->allowField(true)->save($params);
                    $ruleId = $this->model->id;
                    
                    // 插入条件
                    $conditionModel->addConditions($ruleId, $conditions);
                    
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        
        // 获取问卷列表
        $questionnaires = \app\admin\model\policy\Questionnaire::where('status', 'normal')->select();
        $this->view->assign('questionnaires', $questionnaires);
        
        // 获取政策列表
        $policies = \app\admin\model\policy\Policy::where('status', 'normal')->select();
        $this->view->assign('policies', $policies);
        
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->getDetailWithConditions($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                
                // 处理政策ID列表
                if (isset($params['policy_ids']) && is_array($params['policy_ids'])) {
                    $params['policy_ids'] = json_encode($params['policy_ids']);
                } else {
                    $params['policy_ids'] = '[]';
                }
                
                // 处理条件
                $conditions = $this->request->post("conditions/a");
                if (empty($conditions)) {
                    $this->error(__('至少需要一个条件'));
                }
                
                // 验证条件
                $conditionModel = new \app\admin\model\policy\PolicyRuleCondition;
                $validateResult = $conditionModel->validateConditions($conditions);
                if (!$validateResult['valid']) {
                    $this->error($validateResult['message']);
                }
                
                $result = false;
                Db::startTrans();
                try {
                    // 更新规则
                    $result = $row->allowField(true)->save($params);
                    
                    // 更新条件
                    $conditionModel->addConditions($ids, $conditions);
                    
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        
        // 处理政策ID列表
        $row->policy_ids = json_decode($row->policy_ids, true) ?: [];
        
        // 获取问卷列表
        $questionnaires = \app\admin\model\policy\Questionnaire::where('status', 'normal')->select();
        $this->view->assign('questionnaires', $questionnaires);
        
        // 获取政策列表
        $policies = \app\admin\model\policy\Policy::where('status', 'normal')->select();
        $this->view->assign('policies', $policies);
        
        // 获取当前问卷的问题列表
        $questions = [];
        if ($row->questionnaire_id) {
            $questions = \app\admin\model\policy\Question::where('questionnaire_id', $row->questionnaire_id)
                ->where('status', 'normal')
                ->with(['answerOptions' => function($query) {
                    $query->where('status', 'normal')->order('sort', 'asc');
                }])
                ->order('sort', 'asc')
                ->select();
        }
        $this->view->assign('questions', $questions);

        // 处理现有条件数据
        $conditions_data = [];
        \think\Log::write('PolicyRule Edit - Row ID: ' . $row->id, 'info');
        \think\Log::write('PolicyRule Edit - Row Conditions: ' . json_encode($row->conditions), 'info');

        if ($row->conditions && count($row->conditions) > 0) {
            foreach ($row->conditions as $condition) {
                $conditions_data[] = [
                    'question_id'      => (int)$condition->question_id,
                    'answer_option_id' => (int)$condition->answer_option_id,
                    'condition_type'   => $condition->condition_type
                ];
            }
        }

        \think\Log::write('PolicyRule Edit - Conditions Data: ' . json_encode($conditions_data), 'info');

        // 格式化问题数据供JavaScript使用
        $questions_data = [];
        foreach ($questions as $question) {
            $options = [];
            if ($question->answerOptions) {
                foreach ($question->answerOptions as $option) {
                    $options[] = [
                        'id' => $option->id,
                        'title' => $option->title,
                        'sort' => $option->sort
                    ];
                }
            }

            $questions_data[] = [
                'id' => $question->id,
                'title' => $question->title,
                'type' => $question->type,
                'sort' => $question->sort,
                'answer_options' => $options
            ];
        }

        // 生成JavaScript脚本
        $conditions_script = "window.existingConditions = " . json_encode($conditions_data, JSON_UNESCAPED_UNICODE) . ";\n";
        $conditions_script .= "window.questionsData = " . json_encode($questions_data, JSON_UNESCAPED_UNICODE) . ";\n";
        $conditions_script .= "console.log('Script loaded - existingConditions:', window.existingConditions);\n";
        $conditions_script .= "console.log('Script loaded - questionsData:', window.questionsData);";

        \think\Log::write('PolicyRule Edit - Generated Script: ' . $conditions_script, 'info');

        $this->view->assign('conditions_script', $conditions_script);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 更新
     */
    public function update($ids = null)
    {
        $row = $this->model->with('conditions')->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                // 调试信息
                \think\Log::write('PolicyRule Update - Params: ' . json_encode($params), 'info');
                \think\Log::write('PolicyRule Update - Conditions: ' . json_encode($this->request->post('conditions/a', [])), 'info');

                Db::startTrans();
                try {
                    // 更新基本信息
                    $result = $row->allowField(true)->save($params);
                    if ($result !== false) {
                        // 处理条件数据 - 直接获取并验证
                        $conditions = $this->request->post('conditions', []);

                        // 调试信息
                        \think\Log::write('PolicyRule Update - Raw Conditions: ' . json_encode($conditions), 'info');
                        \think\Log::write('PolicyRule Update - Conditions Type: ' . gettype($conditions), 'info');

                        // 解析条件数据
                        $validConditions = [];

                        if (!empty($conditions) && is_array($conditions)) {
                            foreach ($conditions as $index => $condition) {
                                \think\Log::write("Condition {$index}: " . json_encode($condition), 'info');

                                if (is_array($condition)) {
                                    $questionId = $condition['question_id'] ?? '';
                                    $answerOptionId = $condition['answer_option_id'] ?? '';
                                    $conditionType = $condition['condition_type'] ?? 'must';

                                    \think\Log::write("Question ID: {$questionId}, Answer Option ID: {$answerOptionId}", 'info');

                                    if (!empty($questionId) && !empty($answerOptionId)) {
                                        $validConditions[] = [
                                            'question_id' => (int)$questionId,
                                            'answer_option_id' => (int)$answerOptionId,
                                            'condition_type' => $conditionType
                                        ];
                                    } else {
                                        \think\Log::write("Invalid condition - empty question_id or answer_option_id", 'info');
                                    }
                                } else {
                                    \think\Log::write("Invalid condition - not an array: " . json_encode($condition), 'info');
                                }
                            }
                        } else {
                            \think\Log::write('No conditions found or conditions is not an array', 'info');
                        }

                        \think\Log::write('PolicyRule Update - Valid Conditions: ' . json_encode($validConditions), 'info');

                        // 使用模型方法添加条件
                        $conditionModel = new \app\admin\model\policy\PolicyRuleCondition();
                        $conditionModel->addConditions($row->id, $validConditions);

                        Db::commit();
                    }
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 获取问题列表
     */
    public function getQuestions()
    {
        $questionnaireId = $this->request->param('questionnaire_id', 0);
        if (!$questionnaireId) {
            $this->error(__('参数错误'));
        }
        
        $questions = \app\admin\model\policy\Question::where('questionnaire_id', $questionnaireId)
            ->where('status', 'normal')
            ->with(['answerOptions' => function($query) {
                $query->where('status', 'normal')->order('sort', 'asc');
            }])
            ->order('sort', 'asc')
            ->select();
        
        // 格式化数据确保前端能正确解析
        $result = [];
        foreach ($questions as $question) {
            $options = [];
            if ($question->answerOptions) {
                foreach ($question->answerOptions as $option) {
                    $options[] = [
                        'id' => $option->id,
                        'title' => $option->title,
                        'sort' => $option->sort
                    ];
                }
            }
            
            $result[] = [
                'id' => $question->id,
                'title' => $question->title,
                'type' => $question->type,
                'sort' => $question->sort,
                'answer_options' => $options
            ];
        }
        
        $this->success('获取成功', null, $result);
    }

    /**
     * 测试规则匹配
     */
    public function testMatch()
    {
        if ($this->request->isPost()) {
            $ruleId = $this->request->post('rule_id', 0);
            $answers = $this->request->post('answers/a', []);
            
            if (!$ruleId) {
                $this->error(__('参数错误'));
            }
            
            $rule = $this->model->getDetailWithConditions($ruleId);
            if (!$rule) {
                $this->error(__('规则不存在'));
            }
            
            // 格式化用户答案
            $userAnswers = [];
            foreach ($answers as $answer) {
                $userAnswers[] = [
                    'question_id' => $answer['question_id'],
                    'option_ids' => is_array($answer['option_ids']) ? $answer['option_ids'] : [$answer['option_ids']]
                ];
            }
            
            // 测试匹配
            $matchedRules = $this->model->matchUserAnswers($rule->questionnaire_id, $userAnswers);
            $isMatched = false;
            foreach ($matchedRules as $matchedRule) {
                if ($matchedRule['rule_id'] == $ruleId) {
                    $isMatched = true;
                    break;
                }
            }
            
            if ($isMatched) {
                $this->success(__('规则匹配成功'), null, [
                    'matched' => true,
                    'policies' => $rule->policy_list
                ]);
            } else {
                $this->error(__('规则匹配失败'), null, [
                    'matched' => false
                ]);
            }
        }
        
        $this->error(__('非法请求'));
    }
}
