<?php
/**
 * 调试问题管理数据显示问题
 */

// 模拟ThinkPHP环境
define('APP_PATH', __DIR__ . '/application/');
require_once __DIR__ . '/thinkphp/start.php';

use app\admin\model\policy\Question;
use app\admin\model\policy\Questionnaire;

echo "=== 调试问题管理数据显示问题 ===\n\n";

// 1. 检查问卷数据
echo "1. 检查问卷数据：\n";
$questionnaires = Questionnaire::select();
echo "问卷总数：" . count($questionnaires) . "\n";
foreach ($questionnaires as $q) {
    echo "ID: {$q->id}, 标题: {$q->title}, 状态: {$q->status}\n";
}
echo "\n";

// 2. 检查问题数据
echo "2. 检查问题数据：\n";
$questions = Question::select();
echo "问题总数：" . count($questions) . "\n";
foreach ($questions as $q) {
    echo "ID: {$q->id}, 标题: {$q->title}, 问卷ID: {$q->questionnaire_id}, 状态: {$q->status}\n";
}
echo "\n";

// 3. 检查关联查询
echo "3. 检查关联查询：\n";
$questionsWithQuestionnaire = Question::with(['questionnaire', 'answerOptions'])->select();
foreach ($questionsWithQuestionnaire as $q) {
    echo "问题ID: {$q->id}, 标题: {$q->title}\n";
    if ($q->questionnaire) {
        echo "  关联问卷: ID={$q->questionnaire->id}, 标题={$q->questionnaire->title}\n";
    } else {
        echo "  关联问卷: 无 (questionnaire_id={$q->questionnaire_id})\n";
    }
    echo "  选项数量: " . ($q->answerOptions ? count($q->answerOptions) : 0) . "\n";
    echo "\n";
}

// 4. 检查数据库表结构
echo "4. 检查数据库表结构：\n";
try {
    $db = \think\Db::connect();
    
    // 检查policy_question表结构
    $questionFields = $db->query("DESCRIBE fa_policy_question");
    echo "policy_question表字段：\n";
    foreach ($questionFields as $field) {
        echo "  {$field['Field']} - {$field['Type']} - {$field['Null']} - {$field['Default']}\n";
    }
    echo "\n";
    
    // 检查policy_questionnaire表结构
    $questionnaireFields = $db->query("DESCRIBE fa_policy_questionnaire");
    echo "policy_questionnaire表字段：\n";
    foreach ($questionnaireFields as $field) {
        echo "  {$field['Field']} - {$field['Type']} - {$field['Null']} - {$field['Default']}\n";
    }
    echo "\n";
    
} catch (Exception $e) {
    echo "数据库查询错误: " . $e->getMessage() . "\n";
}

// 5. 模拟控制器处理
echo "5. 模拟控制器处理：\n";
$list = Question::with(['questionnaire', 'answerOptions'])
    ->where('status', 'normal')
    ->paginate(10);

foreach ($list as $row) {
    echo "处理问题: {$row->title}\n";
    
    // 设置可见字段
    $row->visible(['id','questionnaire_id','title','description','type','is_required','sort','status','createtime']);
    $row->visible(['type_text','status_text','is_required_text']);
    
    // 处理关联数据
    if ($row->questionnaire) {
        $row->getRelation('questionnaire')->visible(['id','title']);
        echo "  关联问卷处理成功: {$row->questionnaire->title}\n";
    } else {
        echo "  关联问卷处理失败: questionnaire_id={$row->questionnaire_id}\n";
        // 手动设置questionnaire属性
        $row->questionnaire = [
            'id' => 0,
            'title' => '未关联问卷'
        ];
        echo "  设置默认关联问卷\n";
    }
    
    // 处理答案选项
    if ($row->answerOptions) {
        foreach ($row->answerOptions as $option) {
            $option->visible([]);
        }
        $row->option_count = count($row->answerOptions);
        echo "  选项数量: {$row->option_count}\n";
    } else {
        $row->option_count = 0;
        echo "  选项数量: 0\n";
    }
    
    echo "\n";
}

echo "=== 调试完成 ===\n";
?>
