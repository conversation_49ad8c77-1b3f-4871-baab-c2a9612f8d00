<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">政策标题:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text" placeholder="请输入政策标题">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">政策描述:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-description" class="form-control" rows="3" name="row[description]" cols="50" placeholder="请输入政策描述"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">政策内容:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" class="form-control editor" rows="5" name="row[content]" cols="50" placeholder="请输入政策详细内容"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">发布机构:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-publisher" class="form-control" name="row[publisher]" type="text" placeholder="请输入发布机构">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">发布时间:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-publish_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[publish_time]" type="text" value="{:date('Y-m-d H:i:s')}" placeholder="请选择发布时间">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">生效时间:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-effective_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[effective_time]" type="text" placeholder="请选择生效时间">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">失效时间:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-expire_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[expire_time]" type="text" placeholder="请选择失效时间（可选）">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">排序权重:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sort" class="form-control" name="row[sort]" type="number" value="0" placeholder="数值越大排序越靠前">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-status" data-rule="required" class="form-control selectpicker" name="row[status]">
                {foreach name="statusList" item="vo"}
                    <option value="{$key}" {in name="key" value="normal"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
