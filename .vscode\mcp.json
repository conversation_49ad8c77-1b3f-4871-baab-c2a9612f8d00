{"servers": {"memory": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "sequential-thinking": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"CONTEXT7_API_URL": "https://api.context7.com"}}, "fs-mcp-server": {"type": "stdio", "command": "npx", "args": ["-y", "@bunas/fs-mcp@latest", "--API_KEY=asdqwezxc"]}, "browser-tools-mcp": {"type": "stdio", "command": "npx", "args": ["@agentdeskai/browser-tools-mcp@latest"]}, "edgeone-pages-mcp-server": {"type": "stdio", "command": "npx", "args": ["edgeone-pages-mcp"]}, "playwright": {"type": "stdio", "command": "npx", "args": ["@playwright/mcp@latest"]}, "@modelcontextprotocol/knowledge-graph-memory-server": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "ssh-mpc-server": {"type": "stdio", "command": "npx", "args": ["-y", "@fangjunjie/ssh-mcp-server", "--host", "*************", "--port", "22", "--username", "root", "--password", "huohang2024!"]}}}