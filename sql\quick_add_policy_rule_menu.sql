-- 快速添加政策规则管理菜单
-- 简化版本，直接执行即可

-- 1. 确保政策问卷主菜单存在
INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', 0, 'policy', '政策问卷', 'fa fa-file-text-o', '', '政策问卷管理系统', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 'normal');

-- 2. 添加政策规则管理子菜单
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', (SELECT id FROM fa_auth_rule WHERE name = 'policy' LIMIT 1), 'policy/policyrule', '政策规则管理', 'fa fa-cogs', '', '管理政策推荐规则和条件组合', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 50, 'normal');

-- 3. 添加功能权限
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1), 'policy/policyrule/index', '查看', '', '', '查看政策规则列表', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1), 'policy/policyrule/add', '添加', '', '', '添加新的政策规则', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1), 'policy/policyrule/edit', '编辑', '', '', '编辑政策规则', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1), 'policy/policyrule/del', '删除', '', '', '删除政策规则', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1), 'policy/policyrule/multi', '批量操作', '', '', '批量操作政策规则', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1), 'policy/policyrule/getquestions', '获取问题', '', '', '根据问卷获取问题列表', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', (SELECT id FROM fa_auth_rule WHERE name = 'policy/policyrule' LIMIT 1), 'policy/policyrule/testmatch', '测试匹配', '', '', '测试规则匹配功能', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal');

-- 4. 为超级管理员添加权限（简化版）
UPDATE fa_auth_group 
SET rules = CONCAT(IFNULL(rules, ''), ',', (
    SELECT GROUP_CONCAT(id) 
    FROM fa_auth_rule 
    WHERE name = 'policy/policyrule' OR name LIKE 'policy/policyrule/%'
))
WHERE id = 1;

-- 5. 清理重复逗号
UPDATE fa_auth_group 
SET rules = TRIM(BOTH ',' FROM REPLACE(REPLACE(rules, ',,', ','), ',,', ','))
WHERE id = 1;

-- 6. 显示结果
SELECT '政策规则管理菜单添加完成！' as result;

-- 显示菜单结构
SELECT 
    name,
    title,
    CASE 
        WHEN pid = 0 THEN '主菜单'
        WHEN ismenu = 1 THEN '子菜单'
        ELSE '功能权限'
    END as type,
    weigh,
    status
FROM fa_auth_rule 
WHERE name = 'policy/policyrule' OR name LIKE 'policy/policyrule/%'
ORDER BY pid, weigh DESC;
