-- 为订单商品表添加养老顾问分佣字段
-- 解决 "fields not exists:[ylgw_bili]" 错误

-- 1. 为商城订单商品表添加养老顾问分佣字段
ALTER TABLE `fa_wanlshop_order_goods` ADD COLUMN `ylgw_bili` decimal(11,2) DEFAULT 0.00 COMMENT '养老顾问分成金额' AFTER `quyu_d_bili`;
ALTER TABLE `fa_wanlshop_order_goods` ADD COLUMN `ylgw_d_bili` decimal(11,2) DEFAULT 0.00 COMMENT '养老顾问待分成金额' AFTER `ylgw_bili`;

-- 2. 为拼团订单商品表添加养老顾问分佣字段
ALTER TABLE `fa_wanlshop_groups_order_goods` ADD COLUMN `ylgw_bili` decimal(11,2) DEFAULT 0.00 COMMENT '养老顾问分成金额' AFTER `quyu_d_bili`;
ALTER TABLE `fa_wanlshop_groups_order_goods` ADD COLUMN `ylgw_d_bili` decimal(11,2) DEFAULT 0.00 COMMENT '养老顾问待分成金额' AFTER `ylgw_bili`;

-- 3. 为秒杀订单商品表添加养老顾问分佣字段（如果存在）
ALTER TABLE `fa_wanlshop_seckill_order_goods` ADD COLUMN `ylgw_bili` decimal(11,2) DEFAULT 0.00 COMMENT '养老顾问分成金额' AFTER `quyu_d_bili`;
ALTER TABLE `fa_wanlshop_seckill_order_goods` ADD COLUMN `ylgw_d_bili` decimal(11,2) DEFAULT 0.00 COMMENT '养老顾问待分成金额' AFTER `ylgw_bili`;

-- 4. 查看添加结果
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'fa_wanlshop_order_goods' 
AND COLUMN_NAME IN ('ylgw_bili', 'ylgw_d_bili')
ORDER BY COLUMN_NAME;

SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'fa_wanlshop_groups_order_goods' 
AND COLUMN_NAME IN ('ylgw_bili', 'ylgw_d_bili')
ORDER BY COLUMN_NAME;

-- 5. 检查表结构
SHOW COLUMNS FROM `fa_wanlshop_order_goods` LIKE '%ylgw%';
SHOW COLUMNS FROM `fa_wanlshop_groups_order_goods` LIKE '%ylgw%';
