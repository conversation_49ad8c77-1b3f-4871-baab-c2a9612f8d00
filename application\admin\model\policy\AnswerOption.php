<?php

namespace app\admin\model\policy;

use think\Model;
use traits\model\SoftDelete;

class AnswerOption extends Model
{
    use SoftDelete;

    // 表名
    protected $name = 'policy_answer_option';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text'
    ];

    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['sort' => $row[$pk]]);
        });
    }

    /**
     * 获取状态列表
     */
    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'hidden' => __('Hidden')];
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 关联问题
     */
    public function question()
    {
        return $this->belongsTo('Question', 'question_id');
    }

    /**
     * 关联政策（多对多） - 注意：此关联基于旧的表结构，可能需要更新
     */
    public function policies()
    {
        // 旧的关联表 `policy_answer_policy` 已被 `policy_rule` 和 `policy_rule_condition` 替代
        // return $this->belongsToMany('Policy', 'policy_answer_policy', 'policy_id', 'answer_option_id')
        //     ->withField('weight,createtime');
        // 暂时返回一个空关联，避免出错
        return $this->belongsToMany('Policy', 'policy_rule_condition', 'policy_id', 'answer_option_id')->where('1=0');
    }

    /**
     * 获取选项关联的政策ID列表 - 注意：此方法基于旧的表结构，需要更新
     */
    public function getPolicyIds($optionId = null)
    {
        $optionId = $optionId ?: $this->id;
        
        // 旧的关联表 `policy_answer_policy` 已被 `policy_rule` 和 `policy_rule_condition` 替代
        // 这里需要通过规则来获取
        $ruleIds = \think\Db::name('policy_rule_condition')
            ->where('answer_option_id', $optionId)
            ->column('rule_id');
            
        if (empty($ruleIds)) {
            return [];
        }

        $policyIdLists = \think\Db::name('policy_rule')
            ->where('id', 'in', $ruleIds)
            ->column('policy_ids');

        $policyIds = [];
        foreach($policyIdLists as $list) {
            if (!empty($list)) {
                $policyIds = array_merge($policyIds, json_decode($list, true));
            }
        }
        
        return array_unique($policyIds);
    }

    /**
     * 设置选项关联的政策 - 注意：此方法基于旧的表结构，需要更新
     */
    public function setPolicies($optionId, $policyData)
    {
        // 旧的逻辑不再适用，需要通过创建或更新规则来实现
        // 暂时不做任何操作
        return true;
    }

    /**
     * 获取选项的统计信息
     */
    public function getOptionStatistics($optionId = null)
    {
        $optionId = $optionId ?: $this->id;

        // 统计选择次数 - 从新的用户答题记录表统计
        $selectCount = \think\Db::name('policy_user_answer')
            ->where('answer_option_ids', 'like', '%"' . $optionId . '"%')
            ->count();

        // 统计关联规则数量 - 从新的规则条件表统计
        $ruleCount = \think\Db::name('policy_rule_condition')
            ->where('answer_option_id', $optionId)
            ->count();

        // 统计关联政策数量 - 改为统计通过规则关联的政策数量
        $policyCount = count($this->getPolicyIds($optionId));

        return [
            'select_count' => $selectCount,
            'rule_count' => $ruleCount,
            'policy_count' => $policyCount
        ];
    }

    /**
     * 关联政策规则条件
     */
    public function ruleConditions()
    {
        return $this->hasMany('PolicyRuleCondition', 'answer_option_id');
    }

    /**
     * 批量创建答案选项
     */
    public static function createBatch($questionId, $options)
    {
        if (empty($options) || !is_array($options)) {
            return false;
        }
        
        $insertData = [];
        $sort = 1;
        
        foreach ($options as $option) {
            if (empty($option['title'])) {
                continue;
            }
            
            $insertData[] = [
                'question_id' => $questionId,
                'title' => $option['title'],
                'description' => $option['description'] ?? '',
                'sort' => $sort++,
                'status' => $option['status'] ?? 'normal',
                'createtime' => time(),
                'updatetime' => time()
            ];
        }
        
        if (!empty($insertData)) {
            return \think\Db::name('policy_answer_option')->insertAll($insertData);
        }
        
        return false;
    }

    /**
     * 验证答案选项数据
     */
    public function validateOptionData($data)
    {
        $errors = [];

        // 验证问题ID
        if (empty($data['question_id'])) {
            $errors[] = '问题ID不能为空';
        }

        // 验证选项标题
        if (empty($data['title'])) {
            $errors[] = '选项标题不能为空';
        }

        return empty($errors) ? true : $errors;
    }

    /**
     * 获取选项的权重分值 - 注意：此方法基于旧的表结构，需要更新
     */
    public function getWeightByPolicy($optionId, $policyId)
    {
        // 旧的逻辑不再适用，权重现在在规则层面
        return 0;
    }
}
