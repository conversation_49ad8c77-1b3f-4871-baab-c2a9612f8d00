<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\model\User;
use app\common\model\xiluedu\CourseOrder;
use app\common\model\xiluedu\UserCourse;
use think\Db;
use think\Exception;
use think\Validate;

/**
 * 开通养老顾问记录
 *
 * @icon fa fa-user-plus
 */
class YlgwOrder extends Backend
{

    /**
     * YlgwOrder模型对象
     * @var \app\admin\model\xiluedu\CourseOrder
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\xiluedu\CourseOrder;
        
        // 定义开通方式列表
        $this->view->assign("platformList", [
            'quota' => '额度开通',
            'admin' => '管理员开通', 
            'ylgw' => '养老顾问开通', // 重新加入，因为数据中可能存在该类型
            'wxmin' => '自主购买(小程序)'
        ]);
        
        // 定义支付状态列表
        $this->view->assign("payStatusList", [
            '1' => '待支付',
            '2' => '已支付'
        ]);
        
        // 定义支付方式列表
        $this->view->assign("payTypeList", [
            '0' => '无',
            '1' => '微信',
            '2' => '余额',
            '3' => '额度支付'
        ]);
    }

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            
            $where = is_array($where) ? $where : [];
            $filter = $this->request->get("filter");
            $filter = json_decode($filter, true) ?: [];

            if (isset($filter['platform'])) {
                if (strpos($filter['platform'], ',') !== false) {
                    $platforms = explode(',', $filter['platform']);
                    $where['platform'] = ['in', $platforms];
                } else {
                    $where['platform'] = $filter['platform'];
                }
            }

            // 处理 opener_info 搜索条件
            if (isset($filter['opener_info']) && !empty($filter['opener_info'])) {
                $openerInfo = $filter['opener_info'];
                // 模糊搜索 nickname, mobile, realname
                $userIds = \app\common\model\User::where('nickname', 'like', "%{$openerInfo}%")
                                                ->whereOr('mobile', 'like', "%{$openerInfo}%")
                                                ->whereOr('realname', 'like', "%{$openerInfo}%")
                                                ->column('id');
                // 如果找到用户ID，则添加到user_id的where条件中，并包括platform为ylgw的记录
                if (!empty($userIds)) {
                    $where['parent_id'] = ['in', $userIds];
                    // 还需要确保搜索'opener_info'时，如果platform不是'ylgw'，但'opener_info'中包含搜索词，也能被检索到
                    // 考虑到opener_info可能包含'管理员开通'等固定文本，需要更复杂的逻辑
                    // 暂时只处理user_id相关的搜索，对于非用户相关的opener_info，需要前端配合或额外逻辑
                } else {
                    // 如果没有找到匹配的用户ID，可以考虑添加一个始终不匹配的条件，避免返回所有结果
                    // 或者根据业务需求，对'管理员开通'、'自主购买'等进行特殊处理
                    $where['user_id'] = 0; // 确保不返回任何结果
                }
            }
         
            // 移除 course_id = 16 的过滤，以显示所有开通记录
            // $where['course_id'] = 16;

            $list = $this->model
                    ->with(['user', 'course'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);
            //获取执行的sql
            // $sql = $this->model->getLastSql();
            // var_dump($sql); // 调试用，查看SQL语句
            // die;
            foreach ($list as $row) {
                $row->getRelation('user')->visible(['id', 'nickname', 'mobile', 'is_sqdl', 'is_qydl', 'is_ylgw']);
                $row->getRelation('course')->visible(['id', 'name']);
                
                // 添加开通方式描述
                $row->platform_text = $this->getPlatformText($row->platform);
                
                // 添加开通者信息
                $row->opener_info = $this->getOpenerInfo($row);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 获取平台文本描述
     */
    private function getPlatformText($platform)
    {
        $platformList = [
            'quota' => '额度开通',
            'admin' => '管理员开通', 
            // 'ylgw' => '养老顾问开通',
            'wxmin' => '自主购买(小程序)'
        ];
        
        return isset($platformList[$platform]) ? $platformList[$platform] : $platform;
    }

    /**
     * 获取开通者信息
     */
    private function getOpenerInfo($row)
    {
        if ($row->platform == 'admin') {
            return '管理员开通';
        }

        if (in_array($row->platform, ['wxmin'])) {
            return '自主购买';
        }

        if ($row->platform == 'ylgw') {
            return $row->opener_info; // 直接返回opener_info，它包含了HTML代码
        }

        if ($row->platform == 'quota') {
            $user = \app\common\model\User::where('id', $row->user_id)->find();
            if (!$user || !$user->parent_id) {
                return '未知开通者';
            }

            $parent = \app\common\model\User::where('id', $user->parent_id)->find();
            if (!$parent) {
                return '未知开通者';
            }
            $parent->nickname=$parent->realname?$parent->realname:$parent->nickname;
            $role = '';
            if ($parent->is_qydl == 1) {
                $role = '城市运营商';
                 return $parent->nickname . '(' . $role . ')-' . $parent->mobile;
            } elseif ($parent->is_sqdl == 1) {
                $role = '养老院长';
                 return $parent->nickname . '(' . $role . ')-' . $parent->mobile;
            }

            return $parent->nickname .'-' . $parent->mobile;
        }

        return '未知';
    }

    /**
     * 开通养老顾问
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $mobile = isset($params['mobile']) ? $params['mobile'] : '';
                $parentId = isset($params['parent_id']) ? $params['parent_id'] : 0;
                $remark = isset($params['remark']) ? $params['remark'] : '管理员开通';

                if (!$mobile || !Validate::regex($mobile, "^1\d{10}$")) {
                    $this->error('手机号格式错误');
                }

                // 查找或创建目标用户
                $target_user = User::where('mobile', $mobile)->where('is_service', 0)->find();
                if (!$target_user) {
                    $salt = \fast\Random::alnum();
                    $password = \fast\Random::alnum(6);
                    $user_params = [
                        'mobile'   => $mobile,
                        'username' => $mobile,
                        'nickname' => '手机用户' . substr($mobile, -4),
                        'password' => md5(md5($password) . $salt),
                        'salt'     => $salt,
                        'avatar'   => '/assets/img/avatar.png',
                        'parent_id' => $parentId,
                        'status'   => 'normal',
                    ];
                    $target_user = new User();
                    $target_user->save($user_params);
                    $target_user = User::get($target_user->id); // 重新获取以确保数据完整

                    // 确保 service_user_info 表中有记录
                    $userInfo = \app\api\model\service\UserInfo::get(['user_id' => $target_user->id]);
                    if (!$userInfo) {
                        \app\api\model\service\UserInfo::create([
                            'user_id' => $target_user->id,
                            'mobile' => $mobile,
                        ]);
                    }
                }

                // 检查用户状态
                if ($target_user->is_ylgw == 1) $this->error('该用户已经是养老顾问');
                if ($target_user->is_sqdl == 1) $this->error('该用户已经是养老院长');
                if ($target_user->is_qydl == 1) $this->error('该用户已经是城市运营商');
                if ($target_user->parent_id != 0 && $target_user->parent_id != $parentId) {
                    $this->error('该用户已有其他上级，无法重复绑定');
                }
                
                Db::startTrans();
                try {
                    // 更新用户
                    $target_user->is_ylgw = 1;
                    $target_user->parent_id = $parentId;
                    $target_user->save();

                    // 创建订单
                    $order_data = [
                        'platform' => 'admin',
                        'user_id' => $target_user->id,
                        'order_no' => "A" . date("YmdHis") . mt_rand(10, 9999),
                        'course_id' => 16, // 养老顾问课程ID
                        'pay_status' => 2,
                        'pay_type' => 0, // 管理员开通无支付方式
                        'paytime' => time(),
                        'remark' => $remark,
                    ];
                    $this->model->create($order_data);

                    // 创建用户课程
                    UserCourse::create([
                        'user_id' => $target_user->id,
                        'course_id' => 16,
                        'from_type' => 4, // 4代表管理员开通
                    ]);

                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success();
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        
        // 获取上级用户列表
        $parentList = $this->getValidParents();
        $this->view->assign("parentList", $parentList);
        return $this->view->fetch();
    }
    
    /**
     * 获取有效的上级用户列表
     */
    private function getValidParents()
    {
        // "平台"作为默认选项
        $parents = [0 => __('Platform')]; 
        
        // 查询所有非普通用户、非养老顾问的用户（即养老院长和城市运营商）
        $validParents = User::where('is_service', 0)
            ->where(function ($query) {
                $query->where('is_sqdl', 1)->whereOr('is_qydl', 1);
            })
            ->field('id, nickname, realname, mobile')
            ->select();

        foreach ($validParents as $user) {
            $displayName = $user->realname ?: $user->nickname;
            $parents[$user->id] = "{$displayName} ({$user->mobile})";
        }
        
        return $parents;
    }

    /**
     * 禁用编辑功能
     */
    public function edit($ids = null)
    {
        $this->error(__('Operation not allowed'));
    }

    /**
     * 禁用删除功能
     */
    public function del($ids = "")
    {
        $this->error(__('Operation not allowed'));
    }
}
