<?php

namespace app\admin\model\policy;

use think\Model;
use traits\model\SoftDelete;

class Question extends Model
{
    use SoftDelete;

    // 表名
    protected $name = 'policy_question';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'type_text',
        'status_text',
        'is_required_text'
    ];

    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['sort' => $row[$pk]]);
        });
    }

    /**
     * 获取问题类型列表
     */
    public function getTypeList()
    {
        return [
            'single' => '单选题',
            'multiple' => '多选题'
        ];
    }

    /**
     * 获取状态列表
     */
    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'hidden' => __('Hidden')];
    }

    /**
     * 获取是否必答列表
     */
    public function getIsRequiredList()
    {
        return ['0' => '否', '1' => '是'];
    }

    /**
     * 获取问题类型文本
     */
    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type']) ? $data['type'] : '');
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 获取是否必答文本
     */
    public function getIsRequiredTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_required']) ? $data['is_required'] : '');
        $list = $this->getIsRequiredList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 关联问卷
     */
    public function questionnaire()
    {
        return $this->belongsTo('Questionnaire', 'questionnaire_id');
    }

    /**
     * 关联答案选项
     */
    public function answerOptions()
    {
        return $this->hasMany('AnswerOption', 'question_id')->order('sort', 'asc');
    }

    /**
     * 关联用户答题记录
     */
    public function userAnswers()
    {
        return $this->hasMany('UserAnswer', 'question_id');
    }

    /**
     * 关联政策规则条件
     */
    public function ruleConditions()
    {
        return $this->hasMany('PolicyRuleCondition', 'question_id');
    }

    /**
     * 获取问题的统计信息
     */
    public function getQuestionStatistics($id)
    {
        $question = self::get($id, ['answerOptions']);
        if (!$question) {
            return false;
        }

        $statistics = [
            'total_options' => $question->answerOptions->count(),
            'total_answers' => $question->userAnswers()->count(),
            'option_stats' => []
        ];

        // 统计每个选项的选择次数
        foreach ($question->answerOptions as $option) {
            $count = $question->userAnswers()
                ->where('answer_option_ids', 'like', '%' . $option->id . '%')
                ->count();
            
            $statistics['option_stats'][] = [
                'option_id' => $option->id,
                'option_title' => $option->title,
                'count' => $count,
                'percentage' => $statistics['total_answers'] > 0 ? 
                    round($count / $statistics['total_answers'] * 100, 2) : 0
            ];
        }

        return $statistics;
    }

    /**
     * 验证问题数据
     */
    public function validateQuestionData($data)
    {
        $errors = [];

        // 验证问卷ID
        if (empty($data['questionnaire_id'])) {
            $errors[] = '问卷ID不能为空';
        }

        // 验证问题标题
        if (empty($data['title'])) {
            $errors[] = '问题标题不能为空';
        }

        // 验证问题类型
        if (!in_array($data['type'], ['single', 'multiple'])) {
            $errors[] = '问题类型无效';
        }

        return empty($errors) ? true : $errors;
    }

    /**
     * 复制问题到其他问卷
     */
    public function copyToQuestionnaire($questionId, $targetQuestionnaireId)
    {
        $question = self::get($questionId, ['answerOptions']);
        if (!$question) {
            return false;
        }

        // 复制问题
        $newQuestion = $question->toArray();
        unset($newQuestion['id'], $newQuestion['createtime'], $newQuestion['updatetime']);
        $newQuestion['questionnaire_id'] = $targetQuestionnaireId;
        
        $newQuestionModel = new self();
        $newQuestionModel->save($newQuestion);

        // 复制答案选项
        foreach ($question->answerOptions as $option) {
            $newOption = $option->toArray();
            unset($newOption['id'], $newOption['createtime'], $newOption['updatetime']);
            $newOption['question_id'] = $newQuestionModel->id;
            
            $newOptionModel = new AnswerOption();
            $newOptionModel->save($newOption);
        }

        return $newQuestionModel->id;
    }
}
